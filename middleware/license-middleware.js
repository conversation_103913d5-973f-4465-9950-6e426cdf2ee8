const LicenseManager = require('../services/license-service/license-manager');
const FeatureFlagService = require('../services/license-service/feature-flags');
const UsageTracker = require('../services/license-service/usage-tracker');

class LicenseMiddleware {
  constructor(database, secretKey) {
    this.licenseManager = new LicenseManager(secretKey);
    this.featureFlags = new FeatureFlagService();
    this.usageTracker = new UsageTracker(database);
    this.db = database;
  }

  /**
   * Middleware to validate license and extract customer info
   */
  validateLicense() {
    return async (req, res, next) => {
      try {
        const licenseKey = req.headers['x-license-key'];
        const licenseToken = req.headers['x-license-token'];

        if (!licenseKey || !licenseToken) {
          return res.status(401).json({
            error: 'Missing license credentials',
            code: 'MISSING_LICENSE'
          });
        }

        // Validate license
        const validation = this.licenseManager.validateLicense(licenseKey, licenseToken);
        
        if (!validation.valid) {
          return res.status(401).json({
            error: validation.reason,
            code: 'INVALID_LICENSE'
          });
        }

        // Get customer info from database
        const customerResult = await this.db.query(
          'SELECT * FROM customers WHERE id = $1 AND subscription_status = $2',
          [validation.data.customerId, 'active']
        );

        if (customerResult.rows.length === 0) {
          return res.status(401).json({
            error: 'Customer not found or inactive',
            code: 'INACTIVE_CUSTOMER'
          });
        }

        // Attach license and customer info to request
        req.license = validation.data;
        req.customer = customerResult.rows[0];
        req.tier = validation.data.tier;

        next();
      } catch (error) {
        console.error('License validation error:', error);
        res.status(500).json({
          error: 'License validation failed',
          code: 'VALIDATION_ERROR'
        });
      }
    };
  }

  /**
   * Middleware to check if a specific feature is enabled
   */
  requireFeature(feature) {
    return (req, res, next) => {
      if (!req.license) {
        return res.status(401).json({
          error: 'License validation required',
          code: 'NO_LICENSE'
        });
      }

      const hasFeature = this.featureFlags.isFeatureEnabled(feature, req.tier);
      
      if (!hasFeature) {
        return res.status(403).json({
          error: `Feature '${feature}' not available in ${req.tier} tier`,
          code: 'FEATURE_NOT_AVAILABLE',
          requiredTiers: this.featureFlags.featureFlags[feature] || []
        });
      }

      next();
    };
  }

  /**
   * Middleware to track API usage
   */
  trackApiUsage() {
    return async (req, res, next) => {
      if (!req.customer) {
        return next();
      }

      try {
        // Track the API call
        const currentUsage = await this.usageTracker.trackApiUsage(
          req.customer.id,
          req.path,
          req.method
        );

        // Check if approaching limits
        const limitCheck = this.featureFlags.checkApiLimit(req.tier, currentUsage);
        
        if (!limitCheck.allowed) {
          return res.status(429).json({
            error: 'API rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            limit: limitCheck.limit,
            current: currentUsage,
            resetTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          });
        }

        // Add usage info to response headers
        res.set({
          'X-RateLimit-Limit': limitCheck.limit === -1 ? 'unlimited' : limitCheck.limit,
          'X-RateLimit-Remaining': limitCheck.remaining,
          'X-RateLimit-Reset': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        });

        next();
      } catch (error) {
        console.error('API usage tracking error:', error);
        // Don't block the request if tracking fails
        next();
      }
    };
  }

  /**
   * Middleware to check user limits
   */
  checkUserLimits() {
    return async (req, res, next) => {
      if (!req.customer) {
        return next();
      }

      try {
        const currentUsers = await this.usageTracker.getCurrentUserCount(req.customer.id);
        const limitCheck = this.featureFlags.checkUserLimit(req.tier, currentUsers);
        
        if (!limitCheck.allowed) {
          return res.status(403).json({
            error: 'User limit exceeded',
            code: 'USER_LIMIT_EXCEEDED',
            limit: limitCheck.limit,
            current: currentUsers
          });
        }

        req.userLimits = limitCheck;
        next();
      } catch (error) {
        console.error('User limit check error:', error);
        next();
      }
    };
  }

  /**
   * Get license info endpoint
   */
  getLicenseInfo() {
    return (req, res) => {
      if (!req.license || !req.customer) {
        return res.status(401).json({
          error: 'License validation required',
          code: 'NO_LICENSE'
        });
      }

      const featureConfig = this.featureFlags.getFeatureConfig(req.tier);
      
      res.json({
        customer: {
          id: req.customer.id,
          companyName: req.customer.company_name,
          tier: req.tier
        },
        license: {
          tier: req.license.tier,
          expiryDate: req.license.expiryDate,
          maxUsers: req.license.maxUsers,
          apiCallsPerDay: req.license.apiCallsPerDay
        },
        features: featureConfig.features,
        limits: featureConfig.limits
      });
    };
  }

  /**
   * Get usage statistics endpoint
   */
  getUsageStats() {
    return async (req, res) => {
      if (!req.customer) {
        return res.status(401).json({
          error: 'License validation required',
          code: 'NO_LICENSE'
        });
      }

      try {
        const days = parseInt(req.query.days) || 30;
        const summary = await this.usageTracker.getUsageSummary(req.customer.id, days);
        const currentApiUsage = await this.usageTracker.getCurrentApiUsage(req.customer.id);
        const currentUsers = await this.usageTracker.getCurrentUserCount(req.customer.id);
        
        const limits = await this.usageTracker.checkUsageLimits(
          req.customer.id,
          req.tier,
          this.featureFlags
        );

        res.json({
          current: {
            apiCalls: currentApiUsage,
            activeUsers: currentUsers
          },
          limits: limits,
          summary: summary,
          period: `${days} days`
        });
      } catch (error) {
        console.error('Error getting usage stats:', error);
        res.status(500).json({
          error: 'Failed to get usage statistics',
          code: 'USAGE_STATS_ERROR'
        });
      }
    };
  }
}

module.exports = LicenseMiddleware;
