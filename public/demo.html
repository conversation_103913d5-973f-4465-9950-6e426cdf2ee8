<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Licensing Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .license-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .feature-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .feature-enabled {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .feature-disabled {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #28a745;
            color: white;
        }
        .status.error {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Enterprise Licensing System Demo</h1>
            <p>Test the licensing system with different tiers and features</p>
        </div>

        <div class="license-form">
            <h3>License Configuration</h3>
            <div class="form-group">
                <label for="licenseKey">License Key:</label>
                <input type="text" id="licenseKey" placeholder="MYAPP-ENT-20241231-A1B2C3D4">
            </div>
            <div class="form-group">
                <label for="licenseToken">License Token:</label>
                <input type="text" id="licenseToken" placeholder="JWT token here">
            </div>
            <button onclick="generateDemoLicense()">Generate Demo License</button>
            <button onclick="testLicense()">Test License</button>
            <button onclick="testFeatures()">Test Features</button>
        </div>

        <div id="results" class="results" style="display: none;"></div>

        <div id="featureTests" style="display: none;">
            <h3>Feature Tests</h3>
            <div id="featureList"></div>
        </div>

        <div id="usageStats" style="display: none;">
            <h3>Usage Statistics</h3>
            <div id="usageData"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        async function generateDemoLicense() {
            try {
                const response = await fetch('/demo-license.js');
                const script = await response.text();
                
                // This is a demo - in real implementation, license generation would be server-side
                document.getElementById('licenseKey').value = 'MYAPP-ENT-20241231-A1B2C3D4';
                document.getElementById('licenseToken').value = 'demo-token-for-testing';
                
                showResults('Demo license generated! Use the "Test License" button to validate.');
            } catch (error) {
                showResults('Error generating demo license: ' + error.message);
            }
        }

        async function testLicense() {
            const licenseKey = document.getElementById('licenseKey').value;
            const licenseToken = document.getElementById('licenseToken').value;
            
            if (!licenseKey || !licenseToken) {
                showResults('Please enter both license key and token');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/license/info`, {
                    headers: {
                        'X-License-Key': licenseKey,
                        'X-License-Token': licenseToken
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResults(JSON.stringify(data, null, 2));
                    document.getElementById('featureTests').style.display = 'block';
                    displayFeatures(data.features);
                } else {
                    showResults('License validation failed: ' + JSON.stringify(data, null, 2));
                }
            } catch (error) {
                showResults('Error testing license: ' + error.message);
            }
        }

        async function testFeatures() {
            const licenseKey = document.getElementById('licenseKey').value;
            const licenseToken = document.getElementById('licenseToken').value;
            
            const features = [
                { name: 'Basic Users', endpoint: '/api/users' },
                { name: 'Advanced Analytics', endpoint: '/api/analytics/basic' },
                { name: 'API Access', endpoint: '/api/external/data' },
                { name: 'Audit Logs', endpoint: '/api/audit-logs' },
                { name: 'SSO Config', endpoint: '/api/sso/config' },
                { name: 'White Label', endpoint: '/api/white-label/config' }
            ];

            const results = [];
            
            for (const feature of features) {
                try {
                    const response = await fetch(`${API_BASE}${feature.endpoint}`, {
                        headers: {
                            'X-License-Key': licenseKey,
                            'X-License-Token': licenseToken
                        }
                    });
                    
                    const data = await response.json();
                    results.push({
                        feature: feature.name,
                        status: response.ok ? 'success' : 'error',
                        message: response.ok ? 'Accessible' : data.error || 'Access denied',
                        code: data.code
                    });
                } catch (error) {
                    results.push({
                        feature: feature.name,
                        status: 'error',
                        message: error.message
                    });
                }
            }
            
            displayFeatureResults(results);
        }

        function displayFeatures(features) {
            const featureList = document.getElementById('featureList');
            featureList.innerHTML = '';
            
            Object.entries(features).forEach(([feature, enabled]) => {
                const div = document.createElement('div');
                div.className = `feature-test ${enabled ? 'feature-enabled' : 'feature-disabled'}`;
                div.innerHTML = `
                    <strong>${feature}</strong>
                    <span class="status ${enabled ? 'success' : 'error'}">${enabled ? 'ENABLED' : 'DISABLED'}</span>
                `;
                featureList.appendChild(div);
            });
        }

        function displayFeatureResults(results) {
            const featureList = document.getElementById('featureList');
            featureList.innerHTML = '<h4>API Endpoint Tests</h4>';
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `feature-test ${result.status === 'success' ? 'feature-enabled' : 'feature-disabled'}`;
                div.innerHTML = `
                    <strong>${result.feature}</strong>
                    <span class="status ${result.status}">${result.message}</span>
                    ${result.code ? `<br><small>Code: ${result.code}</small>` : ''}
                `;
                featureList.appendChild(div);
            });
        }

        function showResults(text) {
            const results = document.getElementById('results');
            results.textContent = text;
            results.style.display = 'block';
        }

        // Auto-load demo license on page load
        window.onload = function() {
            // Set demo values
            document.getElementById('licenseKey').value = 'MYAPP-ENT-20241231-A1B2C3D4';
            document.getElementById('licenseToken').value = 'demo-token-for-testing';
        };
    </script>
</body>
</html>
