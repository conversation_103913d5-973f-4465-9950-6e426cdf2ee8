# Enterprise Licensing System - Implementation Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Environment Setup](#environment-setup)
3. [Database Configuration](#database-configuration)
4. [License Integration](#license-integration)
5. [Feature Implementation](#feature-implementation)
6. [Client-Side Integration](#client-side-integration)
7. [Testing Strategy](#testing-strategy)
8. [Production Deployment](#production-deployment)

## Quick Start

### 1. Installation
```bash
# Clone the repository
git clone <repository-url>
cd enterprise-licensing

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm run dev
```

### 2. Generate Demo License
```bash
# Generate a test license
node demo-license.js

# Or use the API endpoint
curl http://localhost:3000/demo/generate-license?tier=enterprise
```

### 3. Test the System
```bash
# Test health endpoint
curl http://localhost:3000/health

# Test license validation
curl -H "X-License-Key: YOUR_KEY" \
     -H "X-License-Token: YOUR_TOKEN" \
     http://localhost:3000/api/license/info
```

## Environment Setup

### Required Environment Variables
```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/licensing_db
# For development with SQLite:
# DATABASE_URL=sqlite:./dev.db

# License Security
LICENSE_SECRET_KEY=your-super-secret-key-minimum-32-characters

# Server Configuration
PORT=3000
NODE_ENV=development

# Optional: External Services
STRIPE_SECRET_KEY=sk_test_...
REDIS_URL=redis://localhost:6379
SMTP_URL=smtp://user:<EMAIL>:587
```

### Development vs Production
```javascript
// config/environment.js
const config = {
  development: {
    database: {
      type: 'sqlite',
      storage: ':memory:'
    },
    logging: true,
    cors: { origin: '*' }
  },
  production: {
    database: {
      type: 'postgresql',
      url: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    },
    logging: false,
    cors: { origin: process.env.ALLOWED_ORIGINS?.split(',') }
  }
};
```

## Database Configuration

### PostgreSQL Setup (Recommended for Production)
```bash
# Install PostgreSQL
brew install postgresql  # macOS
sudo apt-get install postgresql  # Ubuntu

# Create database
createdb enterprise_licensing

# Run migrations
psql -d enterprise_licensing -f database/schema.sql

# Verify setup
psql -d enterprise_licensing -c "SELECT table_name FROM information_schema.tables WHERE table_schema='public';"
```

### SQLite Setup (Development)
```javascript
// Automatic setup in app.js
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database(':memory:');

// Tables are created automatically on startup
```

### Database Migrations
```javascript
// migrations/001_initial_schema.js
exports.up = async (db) => {
  await db.query(`
    CREATE TABLE customers (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      company_name VARCHAR(255) NOT NULL,
      contact_email VARCHAR(255) UNIQUE NOT NULL,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `);
};

exports.down = async (db) => {
  await db.query('DROP TABLE customers;');
};
```

## License Integration

### 1. Basic Integration
```javascript
// app.js
const LicenseMiddleware = require('./middleware/license-middleware');
const licenseMiddleware = new LicenseMiddleware(db, process.env.LICENSE_SECRET_KEY);

// Apply to all protected routes
app.use('/api', licenseMiddleware.validateLicense());
app.use('/api', licenseMiddleware.trackApiUsage());
```

### 2. Feature-Specific Protection
```javascript
// Protect specific endpoints
app.get('/api/advanced-analytics', 
  licenseMiddleware.requireFeature('advanced-analytics'),
  async (req, res) => {
    // Only accessible to Professional+ tiers
    const analytics = await getAdvancedAnalytics(req.customer.id);
    res.json(analytics);
  }
);

// Multiple feature requirements
app.get('/api/enterprise-dashboard',
  licenseMiddleware.requireFeature('sso-integration'),
  licenseMiddleware.requireFeature('audit-logs'),
  async (req, res) => {
    // Only accessible to Enterprise+ tiers
    res.json({ message: 'Enterprise dashboard' });
  }
);
```

### 3. Custom License Logic
```javascript
// Custom validation logic
app.use('/api/custom-feature', async (req, res, next) => {
  const { customer, license } = req;
  
  // Custom business logic
  if (customer.subscription_status !== 'active') {
    return res.status(403).json({ error: 'Subscription inactive' });
  }
  
  // Check custom feature flags
  const hasCustomFeature = await checkCustomFeature(customer.id, 'special-feature');
  if (!hasCustomFeature) {
    return res.status(403).json({ error: 'Feature not enabled' });
  }
  
  next();
});
```

## Feature Implementation

### 1. Define Feature Matrix
```javascript
// config/features.js
const FEATURE_MATRIX = {
  // Basic features (all tiers)
  'user-management': ['starter', 'professional', 'enterprise', 'custom'],
  'basic-reporting': ['starter', 'professional', 'enterprise', 'custom'],
  
  // Professional features
  'advanced-analytics': ['professional', 'enterprise', 'custom'],
  'api-access': ['professional', 'enterprise', 'custom'],
  'custom-branding': ['professional', 'enterprise', 'custom'],
  
  // Enterprise features
  'sso-integration': ['enterprise', 'custom'],
  'audit-logs': ['enterprise', 'custom'],
  'advanced-security': ['enterprise', 'custom'],
  
  // Custom features
  'white-label': ['custom'],
  'custom-integrations': ['custom']
};

module.exports = FEATURE_MATRIX;
```

### 2. Implement Feature Checks
```javascript
// services/feature-service.js
class FeatureService {
  constructor(featureMatrix) {
    this.features = featureMatrix;
  }
  
  isEnabled(feature, tier, customFeatures = {}) {
    // Check tier-based features
    if (this.features[feature]?.includes(tier)) {
      return true;
    }
    
    // Check custom features
    return customFeatures[feature] === true;
  }
  
  getAvailableFeatures(tier, customFeatures = {}) {
    const tierFeatures = Object.keys(this.features)
      .filter(feature => this.features[feature].includes(tier));
    
    const enabledCustomFeatures = Object.keys(customFeatures)
      .filter(feature => customFeatures[feature] === true);
    
    return [...new Set([...tierFeatures, ...enabledCustomFeatures])];
  }
}
```

### 3. Usage Tracking Implementation
```javascript
// Track feature usage
async function trackFeatureUsage(customerId, feature, metadata = {}) {
  await db.query(`
    INSERT INTO usage_tracking (customer_id, usage_type, usage_date, count, metadata)
    VALUES ($1, $2, $3, 1, $4)
    ON CONFLICT (customer_id, usage_type, usage_date)
    DO UPDATE SET count = usage_tracking.count + 1
  `, [customerId, `feature:${feature}`, new Date().toISOString().split('T')[0], JSON.stringify(metadata)]);
}

// Usage in middleware
app.use('/api/advanced-analytics', async (req, res, next) => {
  await trackFeatureUsage(req.customer.id, 'advanced-analytics', {
    endpoint: req.path,
    method: req.method
  });
  next();
});
```

## Client-Side Integration

### 1. JavaScript SDK Setup
```javascript
// Initialize the license client
import { LicenseClient } from './license-client';

const licenseClient = new LicenseClient(
  process.env.REACT_APP_API_URL,
  process.env.REACT_APP_LICENSE_KEY,
  process.env.REACT_APP_LICENSE_TOKEN
);

// Initialize and cache license info
await licenseClient.initialize();
```

### 2. React Integration
```jsx
// App.jsx
import { LicenseProvider, useLicense, FeatureGate } from './license-client';

function App() {
  return (
    <LicenseProvider client={licenseClient}>
      <Dashboard />
    </LicenseProvider>
  );
}

function Dashboard() {
  const { hasFeature, licenseInfo, loading } = useLicense();
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>Welcome, {licenseInfo.customer.companyName}</h1>
      <p>Current Tier: {licenseInfo.customer.tier}</p>
      
      {/* Conditional rendering */}
      {hasFeature('advanced-analytics') && (
        <AdvancedAnalytics />
      )}
      
      {/* Feature gate component */}
      <FeatureGate 
        feature="sso-integration"
        fallback={<UpgradePrompt feature="SSO Integration" />}
      >
        <SSOSettings />
      </FeatureGate>
    </div>
  );
}
```

### 3. Vue.js Integration
```javascript
// vue-license-plugin.js
export default {
  install(app, options) {
    const licenseClient = new LicenseClient(options.apiUrl, options.licenseKey, options.licenseToken);
    
    app.config.globalProperties.$license = licenseClient;
    app.provide('license', licenseClient);
  }
};

// main.js
import { createApp } from 'vue';
import LicensePlugin from './vue-license-plugin';

const app = createApp(App);
app.use(LicensePlugin, {
  apiUrl: process.env.VUE_APP_API_URL,
  licenseKey: process.env.VUE_APP_LICENSE_KEY,
  licenseToken: process.env.VUE_APP_LICENSE_TOKEN
});
```

### 4. Angular Integration
```typescript
// license.service.ts
import { Injectable } from '@angular/core';
import { LicenseClient } from './license-client';

@Injectable({
  providedIn: 'root'
})
export class LicenseService {
  private client: LicenseClient;
  
  constructor() {
    this.client = new LicenseClient(
      environment.apiUrl,
      environment.licenseKey,
      environment.licenseToken
    );
  }
  
  async hasFeature(feature: string): Promise<boolean> {
    return this.client.hasFeature(feature);
  }
}

// feature-gate.directive.ts
import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { LicenseService } from './license.service';

@Directive({
  selector: '[featureGate]'
})
export class FeatureGateDirective {
  @Input() set featureGate(feature: string) {
    this.checkFeature(feature);
  }
  
  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private licenseService: LicenseService
  ) {}
  
  private async checkFeature(feature: string) {
    const hasFeature = await this.licenseService.hasFeature(feature);
    
    if (hasFeature) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
```

## Testing Strategy

### 1. Unit Tests
```javascript
// tests/license-manager.test.js
const LicenseManager = require('../services/license-service/license-manager');

describe('LicenseManager', () => {
  let licenseManager;
  
  beforeEach(() => {
    licenseManager = new LicenseManager('test-secret-key');
  });
  
  test('should generate valid license', () => {
    const license = licenseManager.generateLicense(
      'customer-123',
      'enterprise',
      new Date('2024-12-31')
    );
    
    expect(license.licenseKey).toMatch(/^MYAPP-ENT-\d{8}-[A-F0-9]{8}$/);
    expect(license.token).toBeDefined();
  });
  
  test('should validate license correctly', () => {
    const license = licenseManager.generateLicense(
      'customer-123',
      'enterprise',
      new Date('2024-12-31')
    );
    
    const validation = licenseManager.validateLicense(license.licenseKey, license.token);
    expect(validation.valid).toBe(true);
    expect(validation.data.customerId).toBe('customer-123');
  });
});
```

### 2. Integration Tests
```javascript
// tests/api.test.js
const request = require('supertest');
const app = require('../app');

describe('API Endpoints', () => {
  let licenseHeaders;
  
  beforeAll(async () => {
    // Generate test license
    const response = await request(app)
      .get('/demo/generate-license?tier=enterprise');
    
    licenseHeaders = {
      'X-License-Key': response.body.licenseKey,
      'X-License-Token': response.body.licenseToken
    };
  });
  
  test('GET /api/license/info', async () => {
    const response = await request(app)
      .get('/api/license/info')
      .set(licenseHeaders)
      .expect(200);
    
    expect(response.body.customer).toBeDefined();
    expect(response.body.license.tier).toBe('enterprise');
  });
  
  test('GET /api/advanced-analytics requires professional tier', async () => {
    await request(app)
      .get('/api/analytics/basic')
      .set(licenseHeaders)
      .expect(200);
  });
});
```

### 3. Load Testing
```javascript
// tests/load.test.js
const autocannon = require('autocannon');

async function runLoadTest() {
  const result = await autocannon({
    url: 'http://localhost:3000/api/license/info',
    headers: {
      'X-License-Key': 'test-key',
      'X-License-Token': 'test-token'
    },
    connections: 10,
    duration: 30
  });
  
  console.log('Load test results:', result);
}
```

## Production Deployment

### 1. Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
DATABASE_URL=***********************************/licensing
LICENSE_SECRET_KEY=your-production-secret-key
REDIS_URL=redis://prod-redis:6379
PORT=3000

# Security
CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=https://your-sentry-dsn
```

### 2. Docker Deployment
```dockerfile
# Dockerfile
FROM node:16-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:16-alpine AS runtime
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
WORKDIR /app
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --chown=nodejs:nodejs . .
USER nodejs
EXPOSE 3000
CMD ["npm", "start"]
```

### 3. Kubernetes Deployment
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: license-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: license-service
  template:
    metadata:
      labels:
        app: license-service
    spec:
      containers:
      - name: license-service
        image: license-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: license-secrets
              key: database-url
        - name: LICENSE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: license-secrets
              key: secret-key
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 4. Monitoring Setup
```javascript
// monitoring/metrics.js
const prometheus = require('prom-client');

const licenseValidationCounter = new prometheus.Counter({
  name: 'license_validations_total',
  help: 'Total number of license validations',
  labelNames: ['status', 'tier']
});

const apiRequestDuration = new prometheus.Histogram({
  name: 'api_request_duration_seconds',
  help: 'Duration of API requests in seconds',
  labelNames: ['method', 'route', 'status']
});

module.exports = {
  licenseValidationCounter,
  apiRequestDuration,
  register: prometheus.register
};
```

### 5. Performance Optimization
```javascript
// caching/redis-cache.js
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

class CacheService {
  async get(key) {
    return await client.get(key);
  }

  async set(key, value, ttl = 300) {
    return await client.setex(key, ttl, JSON.stringify(value));
  }

  async invalidate(pattern) {
    const keys = await client.keys(pattern);
    if (keys.length > 0) {
      return await client.del(keys);
    }
  }
}

// Usage in license middleware
const cache = new CacheService();

async function validateLicenseWithCache(licenseKey, token) {
  const cacheKey = `license:${licenseKey}`;
  const cached = await cache.get(cacheKey);

  if (cached) {
    return JSON.parse(cached);
  }

  const validation = licenseManager.validateLicense(licenseKey, token);
  if (validation.valid) {
    await cache.set(cacheKey, validation, 300); // 5 minutes
  }

  return validation;
}
```

This implementation guide provides a comprehensive roadmap for integrating the enterprise licensing system into your product. Follow the sections sequentially for a smooth implementation process.
