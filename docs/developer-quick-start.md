# Enterprise Licensing System - Developer Quick Start Guide

## Overview

This guide helps developers quickly integrate the Enterprise Licensing System into their applications. Follow these steps to get up and running in under 30 minutes.

## Prerequisites

- Node.js 16+ installed
- Basic knowledge of Express.js and React/Vue/Angular
- Database (PostgreSQL recommended, SQLite for development)
- Text editor or IDE

## 5-Minute Setup

### Step 1: Clone and Install
```bash
# Clone the repository
git clone <repository-url>
cd enterprise-licensing

# Install dependencies
npm install

# Copy environment template
cp .env.example .env
```

### Step 2: Configure Environment
Edit `.env` file:
```bash
# Required settings
DATABASE_URL=sqlite:./dev.db
LICENSE_SECRET_KEY=your-development-secret-key-32-chars
PORT=3000
NODE_ENV=development

# Optional settings
LOG_LEVEL=debug
```

### Step 3: Start the Server
```bash
# Start development server
npm run dev

# Verify it's running
curl http://localhost:3000/health
```

### Step 4: Generate Demo License
```bash
# Generate a test license
node demo-license.js

# Or use the API
curl http://localhost:3000/demo/generate-license?tier=enterprise
```

### Step 5: Test License Validation
```bash
# Test with generated credentials
curl -H "X-License-Key: MYAPP-ENT-20241231-A1B2C3D4" \
     -H "X-License-Token: eyJhbGciOiJIUzI1NiIs..." \
     http://localhost:3000/api/license/info
```

## Backend Integration

### Express.js Integration (5 minutes)

```javascript
// app.js
const express = require('express');
const LicenseMiddleware = require('./middleware/license-middleware');

const app = express();
const licenseMiddleware = new LicenseMiddleware(db, process.env.LICENSE_SECRET_KEY);

// Apply license validation to all API routes
app.use('/api', licenseMiddleware.validateLicense());
app.use('/api', licenseMiddleware.trackApiUsage());

// Protect specific features
app.get('/api/advanced-feature', 
  licenseMiddleware.requireFeature('advanced-analytics'),
  (req, res) => {
    res.json({ message: 'Advanced feature accessed!' });
  }
);

// Check user limits before adding users
app.post('/api/users',
  licenseMiddleware.checkUserLimits(),
  async (req, res) => {
    // Add user logic here
    res.json({ success: true });
  }
);
```

### Feature Protection Examples

```javascript
// Protect entire route groups
app.use('/api/analytics', licenseMiddleware.requireFeature('advanced-analytics'));
app.use('/api/sso', licenseMiddleware.requireFeature('sso-integration'));

// Conditional feature access
app.get('/api/dashboard', async (req, res) => {
  const data = { basicData: 'available to all' };
  
  if (req.license.tier === 'enterprise' || req.license.tier === 'custom') {
    data.enterpriseData = 'only for enterprise customers';
  }
  
  res.json(data);
});

// Custom validation logic
app.get('/api/custom-endpoint', (req, res, next) => {
  const { customer, license } = req;
  
  // Custom business logic
  if (customer.subscription_status !== 'active') {
    return res.status(403).json({ error: 'Subscription inactive' });
  }
  
  next();
}, handlerFunction);
```

## Frontend Integration

### React Integration (10 minutes)

```jsx
// Install the license client
// (Copy client/license-client.js to your project)

// App.jsx
import React from 'react';
import { LicenseProvider, useLicense, FeatureGate } from './license-client';

// Initialize license client
const licenseClient = new LicenseClient(
  'http://localhost:3000',
  'MYAPP-ENT-20241231-A1B2C3D4',
  'eyJhbGciOiJIUzI1NiIs...'
);

function App() {
  return (
    <LicenseProvider client={licenseClient}>
      <Dashboard />
    </LicenseProvider>
  );
}

function Dashboard() {
  const { hasFeature, licenseInfo, loading } = useLicense();
  
  if (loading) return <div>Loading license...</div>;
  
  return (
    <div>
      <h1>Welcome to {licenseInfo.customer.companyName}</h1>
      <p>Current Plan: {licenseInfo.customer.tier}</p>
      
      {/* Conditional rendering */}
      {hasFeature('advanced-analytics') && (
        <button>View Advanced Analytics</button>
      )}
      
      {/* Feature gate component */}
      <FeatureGate feature="sso-integration">
        <SSOSettings />
      </FeatureGate>
      
      {/* Upgrade prompt for unavailable features */}
      <FeatureGate 
        feature="white-label" 
        fallback={<UpgradePrompt />}
      >
        <WhiteLabelSettings />
      </FeatureGate>
    </div>
  );
}

function UpgradePrompt() {
  return (
    <div className="upgrade-prompt">
      <p>This feature requires a Custom plan.</p>
      <button onClick={() => window.open('/upgrade')}>
        Upgrade Now
      </button>
    </div>
  );
}
```

### Vue.js Integration (5 minutes)

```javascript
// main.js
import { createApp } from 'vue';
import { LicenseClient } from './license-client';

const licenseClient = new LicenseClient(
  'http://localhost:3000',
  'your-license-key',
  'your-license-token'
);

const app = createApp(App);
app.config.globalProperties.$license = licenseClient;
app.mount('#app');

// Component usage
export default {
  async mounted() {
    await this.$license.initialize();
    this.licenseInfo = this.$license.licenseInfo;
  },
  methods: {
    hasFeature(feature) {
      return this.$license.hasFeature(feature);
    }
  },
  template: `
    <div>
      <h1>Dashboard</h1>
      <div v-if="hasFeature('advanced-analytics')">
        <AdvancedAnalytics />
      </div>
      <div v-else>
        <p>Upgrade to Professional for advanced analytics</p>
      </div>
    </div>
  `
};
```

### Angular Integration (5 minutes)

```typescript
// license.service.ts
import { Injectable } from '@angular/core';
import { LicenseClient } from './license-client';

@Injectable({ providedIn: 'root' })
export class LicenseService {
  private client: LicenseClient;
  
  constructor() {
    this.client = new LicenseClient(
      'http://localhost:3000',
      'your-license-key',
      'your-license-token'
    );
  }
  
  async initialize() {
    return this.client.initialize();
  }
  
  hasFeature(feature: string): boolean {
    return this.client.hasFeature(feature);
  }
  
  get licenseInfo() {
    return this.client.licenseInfo;
  }
}

// app.component.ts
import { Component, OnInit } from '@angular/core';
import { LicenseService } from './license.service';

@Component({
  selector: 'app-root',
  template: `
    <div>
      <h1>Dashboard</h1>
      <div *ngIf="hasFeature('advanced-analytics')">
        <app-advanced-analytics></app-advanced-analytics>
      </div>
    </div>
  `
})
export class AppComponent implements OnInit {
  constructor(private licenseService: LicenseService) {}
  
  async ngOnInit() {
    await this.licenseService.initialize();
  }
  
  hasFeature(feature: string): boolean {
    return this.licenseService.hasFeature(feature);
  }
}
```

## Common Integration Patterns

### API Client with License Headers

```javascript
// api-client.js
class APIClient {
  constructor(baseURL, licenseKey, licenseToken) {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
      'X-License-Key': licenseKey,
      'X-License-Token': licenseToken
    };
  }
  
  async request(endpoint, options = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: { ...this.headers, ...options.headers }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'API request failed');
    }
    
    return response.json();
  }
  
  async get(endpoint) {
    return this.request(endpoint);
  }
  
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
}

// Usage
const api = new APIClient(
  'http://localhost:3000',
  'your-license-key',
  'your-license-token'
);

// All requests automatically include license headers
const users = await api.get('/api/users');
const newUser = await api.post('/api/users', { email: '<EMAIL>' });
```

### Feature Flag Hook (React)

```jsx
// hooks/useFeatureFlag.js
import { useLicense } from './license-client';

export function useFeatureFlag(feature) {
  const { hasFeature, getFeatureInfo } = useLicense();
  
  return {
    enabled: hasFeature(feature),
    info: getFeatureInfo(feature)
  };
}

// Usage in components
function AnalyticsButton() {
  const { enabled, info } = useFeatureFlag('advanced-analytics');
  
  if (!enabled) {
    return (
      <button disabled title={`Available in: ${info.availableInTiers.join(', ')}`}>
        Analytics (Upgrade Required)
      </button>
    );
  }
  
  return <button onClick={openAnalytics}>View Analytics</button>;
}
```

### Middleware for Different Frameworks

```javascript
// Express.js middleware
function requireLicense(req, res, next) {
  const licenseKey = req.headers['x-license-key'];
  const licenseToken = req.headers['x-license-token'];
  
  if (!licenseKey || !licenseToken) {
    return res.status(401).json({ error: 'License required' });
  }
  
  // Validate license (implement your validation logic)
  const isValid = validateLicense(licenseKey, licenseToken);
  if (!isValid) {
    return res.status(401).json({ error: 'Invalid license' });
  }
  
  next();
}

// Koa.js middleware
async function koaLicenseMiddleware(ctx, next) {
  const licenseKey = ctx.headers['x-license-key'];
  const licenseToken = ctx.headers['x-license-token'];
  
  if (!licenseKey || !licenseToken) {
    ctx.status = 401;
    ctx.body = { error: 'License required' };
    return;
  }
  
  const isValid = await validateLicense(licenseKey, licenseToken);
  if (!isValid) {
    ctx.status = 401;
    ctx.body = { error: 'Invalid license' };
    return;
  }
  
  await next();
}
```

## Testing Your Integration

### Unit Tests

```javascript
// test/license.test.js
const request = require('supertest');
const app = require('../app');

describe('License Integration', () => {
  const validHeaders = {
    'X-License-Key': 'MYAPP-ENT-20241231-A1B2C3D4',
    'X-License-Token': 'valid-jwt-token'
  };
  
  test('should allow access with valid license', async () => {
    const response = await request(app)
      .get('/api/users')
      .set(validHeaders)
      .expect(200);
    
    expect(response.body).toBeDefined();
  });
  
  test('should deny access without license', async () => {
    await request(app)
      .get('/api/users')
      .expect(401);
  });
  
  test('should enforce feature restrictions', async () => {
    await request(app)
      .get('/api/advanced-analytics')
      .set(validHeaders)
      .expect(200); // Should work for enterprise license
  });
});
```

### Integration Testing

```javascript
// test/integration.test.js
describe('Full Integration Test', () => {
  let licenseClient;
  
  beforeAll(async () => {
    // Generate test license
    const response = await fetch('http://localhost:3000/demo/generate-license');
    const license = await response.json();
    
    licenseClient = new LicenseClient(
      'http://localhost:3000',
      license.licenseKey,
      license.licenseToken
    );
    
    await licenseClient.initialize();
  });
  
  test('should validate license and load features', () => {
    expect(licenseClient.licenseInfo).toBeDefined();
    expect(licenseClient.hasFeature('user-management')).toBe(true);
  });
});
```

## Troubleshooting

### Common Issues

**"License validation failed"**
- Check license key and token format
- Verify secret key matches server configuration
- Ensure license hasn't expired

**"Feature not available"**
- Check if feature is included in current tier
- Verify feature flag configuration
- Check for custom feature overrides

**"Rate limit exceeded"**
- Check current API usage in dashboard
- Verify rate limits for current tier
- Implement request throttling

### Debug Mode

```javascript
// Enable debug logging
process.env.LOG_LEVEL = 'debug';

// Add debug endpoint (development only)
if (process.env.NODE_ENV === 'development') {
  app.get('/debug/license', (req, res) => {
    res.json({
      headers: req.headers,
      license: req.license,
      customer: req.customer,
      tier: req.tier
    });
  });
}
```

## Next Steps

1. **Review the [Implementation Guide](./implementation-guide.md)** for detailed integration instructions
2. **Check the [API Documentation](./api-documentation.md)** for complete endpoint reference
3. **Read the [Security Guide](./security-guide.md)** for production security considerations
4. **Follow the [Deployment Guide](./deployment-guide.md)** for production deployment

## Support

- **Documentation**: Complete guides in the `/docs` folder
- **Demo**: Interactive demo at `http://localhost:3000/demo`
- **Examples**: Sample code in the `/examples` folder
- **Issues**: Report issues in the project repository

**Happy coding! 🚀**
