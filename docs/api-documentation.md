# Enterprise Licensing System - API Documentation

## Base URL
```
Production: https://api.yourdomain.com
Development: http://localhost:3000
```

## Authentication

All protected endpoints require license headers:

```http
X-License-Key: MYAPP-ENT-20241231-A1B2C3D4
X-License-Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Response Format

### Success Response
```json
{
  "data": { ... },
  "meta": {
    "timestamp": "2024-07-11T06:27:18.517Z",
    "version": "1.0.0"
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": { ... },
  "timestamp": "2024-07-11T06:27:18.517Z"
}
```

## Public Endpoints

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-07-11T06:27:18.517Z",
  "checks": {
    "database": "healthy",
    "cache": "healthy"
  }
}
```

### Generate Demo License
```http
GET /demo/generate-license?tier=enterprise
```

**Parameters:**
- `tier` (optional): License tier (starter, professional, enterprise, custom)

**Response:**
```json
{
  "success": true,
  "licenseKey": "MYAPP-ENT-20241231-A1B2C3D4",
  "licenseToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tier": "enterprise",
  "customerId": "customer-123",
  "expiryDate": "2024-12-31T23:59:59.999Z",
  "instructions": {
    "headers": {
      "X-License-Key": "MYAPP-ENT-20241231-A1B2C3D4",
      "X-License-Token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    },
    "testUrl": "http://localhost:3000/api/license/info"
  }
}
```

## License Management Endpoints

### Get License Information
```http
GET /api/license/info
```

**Headers Required:**
- `X-License-Key`
- `X-License-Token`

**Response:**
```json
{
  "customer": {
    "id": "customer-123",
    "companyName": "Demo Company",
    "tier": "enterprise"
  },
  "license": {
    "tier": "enterprise",
    "expiryDate": "2024-12-31T23:59:59.999Z",
    "maxUsers": -1,
    "apiCallsPerDay": -1
  },
  "features": {
    "user-management": true,
    "advanced-analytics": true,
    "sso-integration": true,
    "audit-logs": true,
    "white-label": false
  },
  "limits": {
    "maxUsers": -1,
    "apiCallsPerDay": -1
  }
}
```

### Get Usage Statistics
```http
GET /api/license/usage?days=30
```

**Parameters:**
- `days` (optional): Number of days to include in statistics (default: 30)

**Response:**
```json
{
  "current": {
    "apiCalls": 1250,
    "activeUsers": 45
  },
  "limits": {
    "apiUsage": {
      "allowed": true,
      "limit": -1,
      "current": 1250,
      "remaining": "unlimited"
    },
    "userUsage": {
      "allowed": true,
      "limit": -1,
      "current": 45,
      "remaining": "unlimited"
    },
    "warnings": []
  },
  "summary": {
    "api_calls": {
      "total": 37500,
      "avgDaily": 1250,
      "peakDaily": 2100
    },
    "active_users": {
      "total": 45,
      "avgDaily": 42,
      "peakDaily": 48
    }
  },
  "period": "30 days"
}
```

## User Management Endpoints

### List Users
```http
GET /api/users
```

**Response:**
```json
[
  {
    "id": "user-1",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "admin",
    "status": "active"
  },
  {
    "id": "user-2",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "role": "user",
    "status": "active"
  }
]
```

### Create User
```http
POST /api/users
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "New",
  "lastName": "User",
  "role": "user"
}
```

**Response:**
```json
{
  "id": "user-3",
  "email": "<EMAIL>",
  "first_name": "New",
  "last_name": "User",
  "role": "user",
  "status": "active",
  "created_at": "2024-07-11T06:27:18.517Z"
}
```

**Error Responses:**
- `409 Conflict`: User already exists
- `403 Forbidden`: User limit exceeded

## Analytics Endpoints (Professional+)

### Basic Analytics
```http
GET /api/analytics/basic
```

**Required Feature:** `advanced-analytics`

**Response:**
```json
{
  "total_users": "50",
  "active_users": "45",
  "recent_users": "38"
}
```

## API Access Endpoints (Professional+)

### External Data Access
```http
GET /api/external/data
```

**Required Feature:** `api-access`

**Response:**
```json
{
  "message": "External API data",
  "tier": "enterprise",
  "timestamp": "2024-07-11T06:27:18.517Z"
}
```

## Enterprise Endpoints (Enterprise+)

### Audit Logs
```http
GET /api/audit-logs?page=1&limit=50
```

**Required Feature:** `audit-logs`

**Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 50)

**Response:**
```json
{
  "logs": [
    {
      "id": "log-1",
      "action": "user.created",
      "user_email": "<EMAIL>",
      "resource_type": "user",
      "resource_id": "user-3",
      "details": {},
      "ip_address": "***********",
      "created_at": "2024-07-11T06:27:18.517Z"
    }
  ],
  "page": 1,
  "limit": 50,
  "total": 1
}
```

### SSO Configuration
```http
GET /api/sso/config
```

**Required Feature:** `sso-integration`

**Response:**
```json
[
  {
    "id": "sso-1",
    "provider": "saml",
    "is_active": true
  }
]
```

### Create SSO Configuration
```http
POST /api/sso/config
```

**Request Body:**
```json
{
  "provider": "saml",
  "configuration": {
    "entryPoint": "https://idp.company.com/sso",
    "cert": "-----BEGIN CERTIFICATE-----...",
    "issuer": "company-saml"
  }
}
```

## Custom Tier Endpoints (Custom Only)

### White Label Configuration
```http
GET /api/white-label/config
```

**Required Feature:** `white-label`

**Response:**
```json
{
  "message": "White-label configuration available",
  "tier": "custom",
  "customization": {
    "logo": "/custom-logo.png",
    "colors": {
      "primary": "#your-brand-color",
      "secondary": "#your-secondary-color"
    },
    "domain": "your-custom-domain.com"
  }
}
```

## Admin Endpoints

### Generate License (Admin Only)
```http
POST /admin/license/generate
```

**Request Body:**
```json
{
  "customerId": "customer-456",
  "tier": "enterprise",
  "expiryDate": "2024-12-31T23:59:59.999Z"
}
```

**Response:**
```json
{
  "licenseKey": "MYAPP-ENT-20241231-A1B2C3D4",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tier": "enterprise",
  "expiryDate": "2024-12-31T23:59:59.999Z"
}
```

## Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `MISSING_LICENSE` | License headers not provided | 401 |
| `INVALID_LICENSE` | License validation failed | 401 |
| `INACTIVE_CUSTOMER` | Customer not found or inactive | 401 |
| `FEATURE_NOT_AVAILABLE` | Feature not available in current tier | 403 |
| `USER_LIMIT_EXCEEDED` | User limit reached | 403 |
| `RATE_LIMIT_EXCEEDED` | API rate limit exceeded | 429 |
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `NOT_FOUND` | Endpoint not found | 404 |
| `INTERNAL_ERROR` | Internal server error | 500 |

## Rate Limiting

Rate limits are applied per license tier:

| Tier | API Calls/Day | Burst Limit |
|------|---------------|-------------|
| Starter | 100 | 10/minute |
| Professional | 1,000 | 50/minute |
| Enterprise | Unlimited | 200/minute |
| Custom | Unlimited | Unlimited |

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 2024-07-11T06:27:18.517Z
```

## Webhooks (Coming Soon)

### License Events
- `license.created`
- `license.renewed`
- `license.expired`
- `license.suspended`

### Usage Events
- `usage.limit.warning`
- `usage.limit.exceeded`
- `feature.accessed`

## SDK Examples

### JavaScript/Node.js
```javascript
const { LicenseClient } = require('./license-client');

const client = new LicenseClient(
  'https://api.yourdomain.com',
  'your-license-key',
  'your-license-token'
);

// Check feature availability
if (await client.hasFeature('advanced-analytics')) {
  // Feature is available
}
```

### cURL Examples
```bash
# Get license info
curl -H "X-License-Key: YOUR_KEY" \
     -H "X-License-Token: YOUR_TOKEN" \
     https://api.yourdomain.com/api/license/info

# Create user
curl -X POST \
     -H "X-License-Key: YOUR_KEY" \
     -H "X-License-Token: YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","firstName":"John","lastName":"Doe"}' \
     https://api.yourdomain.com/api/users
```
