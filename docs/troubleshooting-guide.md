# Enterprise Licensing System - Troubleshooting Guide

## Common Issues and Solutions

### License Validation Issues

#### 1. "Invalid License" Error
**Symptoms:**
- API returns `401 Unauthorized` with `INVALID_LICENSE` code
- License validation fails despite correct credentials

**Possible Causes:**
- Expired license token
- Incorrect secret key
- Token tampering
- Clock skew between systems

**Solutions:**
```bash
# Check token expiration
node -e "
const jwt = require('jsonwebtoken');
const token = 'YOUR_TOKEN_HERE';
try {
  const decoded = jwt.decode(token);
  console.log('Token expires:', new Date(decoded.exp * 1000));
  console.log('Current time:', new Date());
} catch (e) {
  console.log('Invalid token format');
}
"

# Verify secret key
echo $LICENSE_SECRET_KEY | wc -c  # Should be at least 32 characters

# Generate new license for testing
curl http://localhost:3000/demo/generate-license?tier=enterprise
```

#### 2. "Customer Not Found" Error
**Symptoms:**
- License validates but customer lookup fails
- `INACTIVE_CUSTOMER` error code

**Solutions:**
```sql
-- Check customer exists in database
SELECT * FROM customers WHERE id = 'customer-123';

-- Check customer status
SELECT id, company_name, subscription_status 
FROM customers 
WHERE subscription_status != 'active';

-- Reactivate customer if needed
UPDATE customers 
SET subscription_status = 'active' 
WHERE id = 'customer-123';
```

### Database Connection Issues

#### 1. Connection Pool Exhaustion
**Symptoms:**
- "Connection pool exhausted" errors
- Slow API responses
- Timeouts

**Solutions:**
```javascript
// Increase pool size
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Increase from default 10
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Monitor pool status
setInterval(() => {
  console.log('Pool status:', {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount
  });
}, 30000);
```

#### 2. Database Migration Failures
**Symptoms:**
- Application fails to start
- Missing table errors
- Schema version conflicts

**Solutions:**
```bash
# Check migration status
psql $DATABASE_URL -c "SELECT * FROM migrations ORDER BY executed_at;"

# Run migrations manually
node scripts/migrate.js

# Reset database (development only)
psql $DATABASE_URL -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
psql $DATABASE_URL -f database/schema.sql
```

### Performance Issues

#### 1. Slow License Validation
**Symptoms:**
- High response times for protected endpoints
- CPU spikes during validation

**Solutions:**
```javascript
// Implement caching
const NodeCache = require('node-cache');
const licenseCache = new NodeCache({ stdTTL: 300 }); // 5 minutes

async function validateLicenseWithCache(licenseKey, token) {
  const cacheKey = `license:${licenseKey}`;
  let validation = licenseCache.get(cacheKey);
  
  if (!validation) {
    validation = licenseManager.validateLicense(licenseKey, token);
    if (validation.valid) {
      licenseCache.set(cacheKey, validation);
    }
  }
  
  return validation;
}

// Monitor cache hit rate
setInterval(() => {
  const stats = licenseCache.getStats();
  console.log('Cache stats:', {
    hits: stats.hits,
    misses: stats.misses,
    hitRate: stats.hits / (stats.hits + stats.misses)
  });
}, 60000);
```

#### 2. High Memory Usage
**Symptoms:**
- Memory leaks
- Out of memory errors
- Gradual performance degradation

**Solutions:**
```javascript
// Monitor memory usage
const memoryUsage = () => {
  const used = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(used.rss / 1024 / 1024) + ' MB',
    heapTotal: Math.round(used.heapTotal / 1024 / 1024) + ' MB',
    heapUsed: Math.round(used.heapUsed / 1024 / 1024) + ' MB',
    external: Math.round(used.external / 1024 / 1024) + ' MB'
  });
};

setInterval(memoryUsage, 30000);

// Force garbage collection (development only)
if (global.gc) {
  setInterval(() => {
    global.gc();
    console.log('Garbage collection triggered');
  }, 60000);
}
```

### API Rate Limiting Issues

#### 1. Unexpected Rate Limit Errors
**Symptoms:**
- `429 Too Many Requests` errors
- Rate limits triggered unexpectedly

**Solutions:**
```javascript
// Debug rate limiting
const rateLimit = require('express-rate-limit');

const debugRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: (req) => ({
    error: 'Rate limit exceeded',
    ip: req.ip,
    tier: req.tier,
    resetTime: new Date(Date.now() + 15 * 60 * 1000)
  }),
  onLimitReached: (req) => {
    console.log('Rate limit reached:', {
      ip: req.ip,
      tier: req.tier,
      path: req.path,
      timestamp: new Date()
    });
  }
});

// Check current usage
app.get('/debug/rate-limit', (req, res) => {
  res.json({
    ip: req.ip,
    tier: req.tier,
    headers: req.headers,
    rateLimit: {
      limit: res.getHeader('X-RateLimit-Limit'),
      remaining: res.getHeader('X-RateLimit-Remaining'),
      reset: res.getHeader('X-RateLimit-Reset')
    }
  });
});
```

### Feature Flag Issues

#### 1. Features Not Available Despite Correct Tier
**Symptoms:**
- `FEATURE_NOT_AVAILABLE` errors
- Features disabled for valid tiers

**Solutions:**
```javascript
// Debug feature flags
app.get('/debug/features', (req, res) => {
  const { tier, customer } = req;
  const featureService = new FeatureFlagService();
  
  res.json({
    tier,
    customerId: customer?.id,
    availableFeatures: featureService.getEnabledFeatures(tier),
    allFeatures: featureService.featureFlags,
    customFeatures: customer?.custom_features || {}
  });
});

// Test specific feature
function debugFeatureCheck(feature, tier) {
  const featureService = new FeatureFlagService();
  const isEnabled = featureService.isFeatureEnabled(feature, tier);
  
  console.log('Feature check:', {
    feature,
    tier,
    enabled: isEnabled,
    allowedTiers: featureService.featureFlags[feature] || 'Not found'
  });
  
  return isEnabled;
}
```

### Deployment Issues

#### 1. Container Startup Failures
**Symptoms:**
- Pods crash on startup
- Health check failures
- Service unavailable

**Solutions:**
```bash
# Check pod logs
kubectl logs -f deployment/license-service -n licensing-system

# Check pod status
kubectl get pods -n licensing-system
kubectl describe pod <pod-name> -n licensing-system

# Check environment variables
kubectl exec -it <pod-name> -n licensing-system -- env | grep LICENSE

# Test health endpoint
kubectl exec -it <pod-name> -n licensing-system -- curl localhost:3000/health
```

#### 2. Database Connection in Kubernetes
**Symptoms:**
- "Connection refused" errors
- DNS resolution failures

**Solutions:**
```bash
# Check service connectivity
kubectl exec -it <pod-name> -n licensing-system -- nslookup postgres

# Test database connection
kubectl exec -it <pod-name> -n licensing-system -- \
  psql $DATABASE_URL -c "SELECT 1;"

# Check secrets
kubectl get secret license-secrets -n licensing-system -o yaml
```

## Debugging Tools

### 1. Health Check Endpoint
```javascript
// Enhanced health check
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
    uptime: process.uptime(),
    checks: {}
  };

  try {
    // Database check
    const dbResult = await db.query('SELECT 1');
    health.checks.database = 'healthy';
  } catch (error) {
    health.checks.database = 'unhealthy';
    health.status = 'unhealthy';
  }

  try {
    // Cache check (if using Redis)
    if (redisClient) {
      await redisClient.ping();
      health.checks.cache = 'healthy';
    }
  } catch (error) {
    health.checks.cache = 'unhealthy';
  }

  // Memory check
  const memUsage = process.memoryUsage();
  health.checks.memory = {
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    status: memUsage.heapUsed / memUsage.heapTotal < 0.9 ? 'healthy' : 'warning'
  };

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});
```

### 2. Debug Endpoints
```javascript
// Debug information (development only)
if (process.env.NODE_ENV === 'development') {
  app.get('/debug/info', (req, res) => {
    res.json({
      environment: process.env.NODE_ENV,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      env: {
        DATABASE_URL: process.env.DATABASE_URL ? 'Set' : 'Not set',
        LICENSE_SECRET_KEY: process.env.LICENSE_SECRET_KEY ? 'Set' : 'Not set',
        REDIS_URL: process.env.REDIS_URL ? 'Set' : 'Not set'
      }
    });
  });

  app.get('/debug/database', async (req, res) => {
    try {
      const tables = await db.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      const counts = {};
      for (const table of tables.rows) {
        const result = await db.query(`SELECT COUNT(*) FROM ${table.table_name}`);
        counts[table.table_name] = parseInt(result.rows[0].count);
      }
      
      res.json({
        tables: tables.rows.map(r => r.table_name),
        counts
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}
```

### 3. Logging Configuration
```javascript
// Enhanced logging for debugging
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        ...meta,
        service: 'license-service',
        version: process.env.npm_package_version
      });
    })
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      tier: req.tier,
      customerId: req.customer?.id
    });
  });
  
  next();
});
```

## Performance Monitoring

### 1. Metrics Collection
```javascript
// Custom metrics
const prometheus = require('prom-client');

const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'tier']
});

const licenseValidations = new prometheus.Counter({
  name: 'license_validations_total',
  help: 'Total number of license validations',
  labelNames: ['status', 'tier']
});

// Metrics middleware
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path, res.statusCode, req.tier || 'unknown')
      .observe(duration);
  });
  
  next();
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  res.set('Content-Type', prometheus.register.contentType);
  res.end(prometheus.register.metrics());
});
```

### 2. Alert Configuration
```yaml
# alerts.yml (Prometheus AlertManager)
groups:
- name: license-service
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High error rate detected
      
  - alert: LicenseValidationFailures
    expr: rate(license_validations_total{status="failed"}[5m]) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: High license validation failure rate
```

This troubleshooting guide provides comprehensive solutions for common issues and debugging tools to maintain the enterprise licensing system effectively.
