# Enterprise Licensing System - Security Guide

## Security Overview

The Enterprise Licensing System implements multiple layers of security to protect license data, customer information, and system integrity. This guide covers security best practices, threat mitigation, and compliance considerations.

## License Security

### 1. JWT Token Security

#### Token Structure
```javascript
// JWT Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// JWT Payload
{
  "customerId": "customer-123",
  "tier": "enterprise",
  "expiryDate": "2024-12-31T23:59:59.999Z",
  "features": ["all-features", "sso-integration"],
  "maxUsers": -1,
  "apiCallsPerDay": -1,
  "generatedAt": "2024-07-11T06:27:18.517Z",
  "iat": **********,
  "exp": **********
}

// JWT Signature (HMAC SHA-256)
```

#### Key Management Best Practices
```javascript
// Environment-based key management
const SECRET_KEY = process.env.LICENSE_SECRET_KEY;

// Key rotation strategy
class KeyManager {
  constructor() {
    this.currentKey = process.env.LICENSE_SECRET_KEY;
    this.previousKey = process.env.LICENSE_SECRET_KEY_PREVIOUS;
  }
  
  sign(payload) {
    return jwt.sign(payload, this.currentKey, { algorithm: 'HS256' });
  }
  
  verify(token) {
    try {
      return jwt.verify(token, this.currentKey);
    } catch (error) {
      // Try previous key for graceful rotation
      if (this.previousKey) {
        return jwt.verify(token, this.previousKey);
      }
      throw error;
    }
  }
}
```

### 2. License Key Generation
```javascript
// Cryptographically secure license key generation
const crypto = require('crypto');

function generateSecureLicenseKey(customerId, tier, expiryDate) {
  const tierCode = tier.substring(0, 3).toUpperCase();
  const expiryCode = expiryDate.toISOString().substring(0, 10).replace(/-/g, '');
  
  // Generate cryptographic hash
  const data = `${customerId}:${tier}:${expiryDate.getTime()}`;
  const hash = crypto.createHmac('sha256', process.env.LICENSE_SECRET_KEY)
    .update(data)
    .digest('hex')
    .substring(0, 8)
    .toUpperCase();
  
  return `MYAPP-${tierCode}-${expiryCode}-${hash}`;
}
```

## API Security

### 1. Authentication & Authorization
```javascript
// Multi-layer authentication
const securityMiddleware = [
  helmet(), // Security headers
  cors(corsOptions), // CORS configuration
  rateLimit(rateLimitConfig), // Rate limiting
  licenseValidation, // License validation
  featureAuthorization // Feature-based authorization
];

app.use('/api', securityMiddleware);
```

### 2. Input Validation
```javascript
const { body, param, query, validationResult } = require('express-validator');

// Request validation middleware
const validateCreateUser = [
  body('email').isEmail().normalizeEmail(),
  body('firstName').trim().isLength({ min: 1, max: 50 }),
  body('lastName').trim().isLength({ min: 1, max: 50 }),
  body('role').isIn(['user', 'admin', 'manager']),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }
    next();
  }
];

app.post('/api/users', validateCreateUser, createUserHandler);
```

### 3. SQL Injection Prevention
```javascript
// Always use parameterized queries
async function getUsersByCustomer(customerId) {
  // SECURE: Parameterized query
  const result = await db.query(
    'SELECT * FROM users WHERE customer_id = $1 AND status = $2',
    [customerId, 'active']
  );
  
  // INSECURE: String concatenation (DON'T DO THIS)
  // const result = await db.query(
  //   `SELECT * FROM users WHERE customer_id = '${customerId}'`
  // );
  
  return result.rows;
}
```

## Data Protection

### 1. Encryption at Rest
```javascript
// Database encryption configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: {
    require: true,
    rejectUnauthorized: false
  },
  // Enable encryption at rest
  encrypt: true,
  options: {
    encrypt: true,
    trustServerCertificate: false
  }
};
```

### 2. PII Data Handling
```javascript
// PII encryption utilities
const crypto = require('crypto');

class PIIProtection {
  constructor(encryptionKey) {
    this.algorithm = 'aes-256-gcm';
    this.key = crypto.scryptSync(encryptionKey, 'salt', 32);
  }
  
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(
      this.algorithm, 
      this.key, 
      Buffer.from(encryptedData.iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// Usage in user management
const piiProtection = new PIIProtection(process.env.PII_ENCRYPTION_KEY);

async function createUser(userData) {
  const encryptedEmail = piiProtection.encrypt(userData.email);
  
  await db.query(
    'INSERT INTO users (customer_id, encrypted_email, first_name, last_name) VALUES ($1, $2, $3, $4)',
    [userData.customerId, JSON.stringify(encryptedEmail), userData.firstName, userData.lastName]
  );
}
```

### 3. Audit Logging
```javascript
// Comprehensive audit logging
class AuditLogger {
  constructor(database) {
    this.db = database;
  }
  
  async log(action, details) {
    const auditEntry = {
      customer_id: details.customerId,
      user_id: details.userId,
      action: action,
      resource_type: details.resourceType,
      resource_id: details.resourceId,
      details: details.metadata || {},
      ip_address: details.ipAddress,
      user_agent: details.userAgent,
      timestamp: new Date().toISOString()
    };
    
    await this.db.query(`
      INSERT INTO audit_logs (
        customer_id, user_id, action, resource_type, resource_id,
        details, ip_address, user_agent, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      auditEntry.customer_id,
      auditEntry.user_id,
      auditEntry.action,
      auditEntry.resource_type,
      auditEntry.resource_id,
      JSON.stringify(auditEntry.details),
      auditEntry.ip_address,
      auditEntry.user_agent,
      auditEntry.timestamp
    ]);
  }
}

// Audit middleware
const auditLogger = new AuditLogger(db);

function auditMiddleware(action, resourceType) {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log after successful operation
      if (res.statusCode < 400) {
        auditLogger.log(action, {
          customerId: req.customer?.id,
          userId: req.user?.id,
          resourceType,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          metadata: {
            method: req.method,
            path: req.path,
            statusCode: res.statusCode
          }
        });
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
}

// Usage
app.post('/api/users', 
  auditMiddleware('user.created', 'user'),
  createUserHandler
);
```

## Network Security

### 1. HTTPS Configuration
```javascript
// Production HTTPS setup
const https = require('https');
const fs = require('fs');

const httpsOptions = {
  key: fs.readFileSync(process.env.SSL_PRIVATE_KEY_PATH),
  cert: fs.readFileSync(process.env.SSL_CERTIFICATE_PATH),
  ca: fs.readFileSync(process.env.SSL_CA_PATH)
};

const server = https.createServer(httpsOptions, app);
server.listen(443, () => {
  console.log('HTTPS Server running on port 443');
});

// Redirect HTTP to HTTPS
const http = require('http');
http.createServer((req, res) => {
  res.writeHead(301, { 
    Location: `https://${req.headers.host}${req.url}` 
  });
  res.end();
}).listen(80);
```

### 2. Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

// Tier-based rate limiting
const createRateLimiter = (tier) => {
  const limits = {
    starter: { windowMs: 15 * 60 * 1000, max: 100 },
    professional: { windowMs: 15 * 60 * 1000, max: 1000 },
    enterprise: { windowMs: 15 * 60 * 1000, max: 10000 },
    custom: { windowMs: 15 * 60 * 1000, max: 50000 }
  };
  
  return rateLimit({
    ...limits[tier],
    message: {
      error: 'Rate limit exceeded',
      tier: tier,
      retryAfter: limits[tier].windowMs / 1000
    },
    standardHeaders: true,
    legacyHeaders: false
  });
};

// Dynamic rate limiting based on license tier
app.use('/api', (req, res, next) => {
  if (req.tier) {
    const limiter = createRateLimiter(req.tier);
    limiter(req, res, next);
  } else {
    next();
  }
});
```

## Compliance & Standards

### 1. GDPR Compliance
```javascript
// GDPR data handling
class GDPRCompliance {
  async handleDataRequest(customerId, requestType) {
    switch (requestType) {
      case 'access':
        return await this.exportCustomerData(customerId);
      case 'deletion':
        return await this.deleteCustomerData(customerId);
      case 'portability':
        return await this.exportPortableData(customerId);
      default:
        throw new Error('Invalid request type');
    }
  }
  
  async exportCustomerData(customerId) {
    const customer = await db.query('SELECT * FROM customers WHERE id = $1', [customerId]);
    const users = await db.query('SELECT * FROM users WHERE customer_id = $1', [customerId]);
    const usage = await db.query('SELECT * FROM usage_tracking WHERE customer_id = $1', [customerId]);
    
    return {
      customer: customer.rows[0],
      users: users.rows,
      usage: usage.rows,
      exportDate: new Date().toISOString()
    };
  }
  
  async deleteCustomerData(customerId) {
    // Soft delete with audit trail
    await db.query('UPDATE customers SET deleted_at = NOW() WHERE id = $1', [customerId]);
    await db.query('UPDATE users SET deleted_at = NOW() WHERE customer_id = $1', [customerId]);
    
    // Log deletion
    await auditLogger.log('gdpr.data_deletion', {
      customerId,
      resourceType: 'customer_data',
      metadata: { reason: 'gdpr_request' }
    });
  }
}
```

### 2. SOC 2 Compliance
```javascript
// SOC 2 security controls
const securityControls = {
  // Access controls
  authentication: 'Multi-factor authentication required',
  authorization: 'Role-based access control implemented',
  
  // System monitoring
  logging: 'Comprehensive audit logging enabled',
  monitoring: 'Real-time security monitoring active',
  
  // Data protection
  encryption: 'Data encrypted at rest and in transit',
  backup: 'Regular encrypted backups performed',
  
  // Incident response
  alerting: 'Automated security alerts configured',
  response: 'Incident response procedures documented'
};

// Security monitoring
class SecurityMonitor {
  constructor() {
    this.alerts = [];
  }
  
  detectAnomalies(req) {
    // Detect suspicious patterns
    const suspiciousPatterns = [
      this.detectBruteForce(req),
      this.detectUnusualAccess(req),
      this.detectDataExfiltration(req)
    ];
    
    suspiciousPatterns.forEach(pattern => {
      if (pattern.detected) {
        this.triggerAlert(pattern);
      }
    });
  }
  
  triggerAlert(pattern) {
    const alert = {
      type: pattern.type,
      severity: pattern.severity,
      details: pattern.details,
      timestamp: new Date().toISOString()
    };
    
    // Send to security team
    this.sendSecurityAlert(alert);
    
    // Log for audit
    auditLogger.log('security.alert', {
      resourceType: 'security_event',
      metadata: alert
    });
  }
}
```

## Security Testing

### 1. Penetration Testing Checklist
```javascript
// Security test scenarios
const securityTests = [
  'SQL injection attempts',
  'XSS payload injection',
  'CSRF token validation',
  'Authentication bypass attempts',
  'Authorization escalation tests',
  'Rate limiting validation',
  'Input validation testing',
  'Session management tests',
  'Encryption verification',
  'API security testing'
];

// Automated security testing
const securityTestSuite = {
  testSQLInjection: async () => {
    const maliciousInputs = [
      "'; DROP TABLE users; --",
      "1' OR '1'='1",
      "admin'/*",
      "1; SELECT * FROM licenses"
    ];
    
    for (const input of maliciousInputs) {
      const response = await request(app)
        .post('/api/users')
        .send({ email: input })
        .expect(400); // Should be rejected
    }
  },
  
  testRateLimit: async () => {
    const requests = Array(200).fill().map(() => 
      request(app).get('/api/license/info')
    );
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.filter(r => r.status === 429);
    
    expect(rateLimited.length).toBeGreaterThan(0);
  }
};
```

This security guide provides comprehensive protection strategies for the enterprise licensing system. Regular security audits and updates are essential for maintaining system integrity.
