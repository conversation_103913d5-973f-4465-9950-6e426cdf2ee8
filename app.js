const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const LicenseMiddleware = require('./middleware/license-middleware');

const app = express();
const port = process.env.PORT || 3000;

// Database connection
const db = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/enterprise_licensing',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Initialize license middleware
const licenseMiddleware = new LicenseMiddleware(db, process.env.LICENSE_SECRET_KEY || 'your-secret-key');

// Middleware
app.use(cors());
app.use(express.json());

// Public routes (no license required)
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// License validation for all protected routes
app.use('/api', licenseMiddleware.validateLicense());
app.use('/api', licenseMiddleware.trackApiUsage());

// License info endpoints
app.get('/api/license/info', licenseMiddleware.getLicenseInfo());
app.get('/api/license/usage', licenseMiddleware.getUsageStats());

// Basic features (available to all tiers)
app.get('/api/users', async (req, res) => {
  try {
    const result = await db.query(
      'SELECT id, email, first_name, last_name, role, status FROM users WHERE customer_id = $1',
      [req.customer.id]
    );
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

app.post('/api/users', licenseMiddleware.checkUserLimits(), async (req, res) => {
  try {
    const { email, firstName, lastName, role = 'user' } = req.body;
    
    const result = await db.query(
      'INSERT INTO users (customer_id, email, first_name, last_name, role) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [req.customer.id, email, firstName, lastName, role]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      res.status(409).json({ error: 'User already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
});

// Professional tier and above features
app.get('/api/analytics/basic', 
  licenseMiddleware.requireFeature('advanced-analytics'),
  async (req, res) => {
    try {
      const stats = await db.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN last_login_at > NOW() - INTERVAL '30 days' THEN 1 END) as recent_users
        FROM users 
        WHERE customer_id = $1
      `, [req.customer.id]);
      
      res.json(stats.rows[0]);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch analytics' });
    }
  }
);

// API access (Professional tier and above)
app.get('/api/external/data',
  licenseMiddleware.requireFeature('api-access'),
  (req, res) => {
    res.json({
      message: 'External API data',
      tier: req.tier,
      timestamp: new Date().toISOString()
    });
  }
);

// Enterprise tier features
app.get('/api/audit-logs',
  licenseMiddleware.requireFeature('audit-logs'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      const offset = (page - 1) * limit;
      
      const result = await db.query(`
        SELECT 
          al.*,
          u.email as user_email
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.customer_id = $1
        ORDER BY al.created_at DESC
        LIMIT $2 OFFSET $3
      `, [req.customer.id, limit, offset]);
      
      res.json({
        logs: result.rows,
        page,
        limit,
        total: result.rowCount
      });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch audit logs' });
    }
  }
);

// SSO configuration (Enterprise tier)
app.get('/api/sso/config',
  licenseMiddleware.requireFeature('sso-integration'),
  async (req, res) => {
    try {
      const result = await db.query(
        'SELECT id, provider, is_active FROM sso_configurations WHERE customer_id = $1',
        [req.customer.id]
      );
      res.json(result.rows);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch SSO configuration' });
    }
  }
);

app.post('/api/sso/config',
  licenseMiddleware.requireFeature('sso-integration'),
  async (req, res) => {
    try {
      const { provider, configuration } = req.body;
      
      const result = await db.query(
        'INSERT INTO sso_configurations (customer_id, provider, configuration) VALUES ($1, $2, $3) RETURNING *',
        [req.customer.id, provider, configuration]
      );
      
      res.status(201).json(result.rows[0]);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create SSO configuration' });
    }
  }
);

// Custom tier features
app.get('/api/white-label/config',
  licenseMiddleware.requireFeature('white-label'),
  async (req, res) => {
    res.json({
      message: 'White-label configuration available',
      tier: req.tier,
      customization: {
        logo: '/custom-logo.png',
        colors: {
          primary: '#your-brand-color',
          secondary: '#your-secondary-color'
        },
        domain: 'your-custom-domain.com'
      }
    });
  }
);

// Admin endpoints for license management
app.post('/admin/license/generate', async (req, res) => {
  // This should be protected with admin authentication
  try {
    const { customerId, tier, expiryDate } = req.body;
    
    const license = licenseMiddleware.licenseManager.generateLicense(
      customerId,
      tier,
      new Date(expiryDate)
    );
    
    // Save to database
    await db.query(
      'INSERT INTO licenses (customer_id, license_key, license_token, tier, expires_at) VALUES ($1, $2, $3, $4, $5)',
      [customerId, license.licenseKey, license.token, tier, expiryDate]
    );
    
    res.json({
      licenseKey: license.licenseKey,
      token: license.token,
      tier,
      expiryDate
    });
  } catch (error) {
    console.error('License generation error:', error);
    res.status(500).json({ error: 'Failed to generate license' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  });
});

app.listen(port, () => {
  console.log(`Enterprise licensing server running on port ${port}`);
});

module.exports = app;
