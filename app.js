const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const LicenseMiddleware = require('./middleware/license-middleware');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3000;

// Database connection (SQLite for local development)
const db = new sqlite3.Database(':memory:');

// Mock database adapter to match PostgreSQL interface
const dbAdapter = {
  query: (sql, params = []) => {
    return new Promise((resolve, reject) => {
      // Convert PostgreSQL syntax to SQLite
      let sqliteSql = sql
        .replace(/\$(\d+)/g, '?')
        .replace(/gen_random_uuid\(\)/g, "lower(hex(randomblob(16)))")
        .replace(/NOW\(\)/g, "datetime('now')")
        .replace(/TIMESTAMP/g, 'TEXT')
        .replace(/UUID/g, 'TEXT')
        .replace(/JSONB/g, 'TEXT')
        .replace(/INET/g, 'TEXT');

      if (sql.includes('SELECT') || sql.includes('INSERT') || sql.includes('UPDATE')) {
        db.all(sqliteSql, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({ rows: rows || [], rowCount: rows ? rows.length : 0 });
          }
        });
      } else {
        db.run(sqliteSql, params, function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ rows: [], rowCount: this.changes });
          }
        });
      }
    });
  }
};

// Initialize database tables
const initDatabase = () => {
  const tables = [
    `CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      company_name TEXT NOT NULL,
      contact_email TEXT NOT NULL UNIQUE,
      billing_email TEXT,
      subscription_status TEXT DEFAULT 'active',
      created_at TEXT DEFAULT (datetime('now'))
    )`,
    `CREATE TABLE IF NOT EXISTS licenses (
      id TEXT PRIMARY KEY,
      customer_id TEXT NOT NULL,
      license_key TEXT UNIQUE NOT NULL,
      license_token TEXT NOT NULL,
      tier TEXT NOT NULL,
      max_users INTEGER DEFAULT -1,
      expires_at TEXT NOT NULL,
      is_active INTEGER DEFAULT 1,
      created_at TEXT DEFAULT (datetime('now'))
    )`,
    `CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      customer_id TEXT NOT NULL,
      email TEXT NOT NULL,
      first_name TEXT,
      last_name TEXT,
      role TEXT DEFAULT 'user',
      status TEXT DEFAULT 'active',
      created_at TEXT DEFAULT (datetime('now'))
    )`,
    `CREATE TABLE IF NOT EXISTS usage_tracking (
      id TEXT PRIMARY KEY,
      customer_id TEXT NOT NULL,
      usage_type TEXT NOT NULL,
      usage_date TEXT NOT NULL,
      count INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now'))
    )`
  ];

  tables.forEach(sql => {
    db.run(sql, (err) => {
      if (err) console.error('Error creating table:', err);
    });
  });

  // Insert sample data
  const sampleCustomerId = 'customer-123';
  const sampleData = [
    `INSERT OR IGNORE INTO customers (id, company_name, contact_email) VALUES ('${sampleCustomerId}', 'Demo Company', '<EMAIL>')`,
    `INSERT OR IGNORE INTO users (id, customer_id, email, first_name, last_name) VALUES ('user-1', '${sampleCustomerId}', '<EMAIL>', 'John', 'Doe')`,
    `INSERT OR IGNORE INTO users (id, customer_id, email, first_name, last_name) VALUES ('user-2', '${sampleCustomerId}', '<EMAIL>', 'Jane', 'Smith')`
  ];

  sampleData.forEach(sql => {
    db.run(sql, (err) => {
      if (err) console.error('Error inserting sample data:', err);
    });
  });
};

initDatabase();

// Initialize license middleware
const licenseMiddleware = new LicenseMiddleware(dbAdapter, process.env.LICENSE_SECRET_KEY || 'your-secret-key');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Public routes (no license required)
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Demo license generation endpoint
app.get('/demo/generate-license', (req, res) => {
  try {
    const customerId = 'customer-123';
    const tier = req.query.tier || 'enterprise';
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);

    const license = licenseMiddleware.licenseManager.generateLicense(customerId, tier, expiryDate);

    res.json({
      success: true,
      licenseKey: license.licenseKey,
      licenseToken: license.token,
      tier,
      customerId,
      expiryDate: expiryDate.toISOString(),
      instructions: {
        headers: {
          'X-License-Key': license.licenseKey,
          'X-License-Token': license.token
        },
        testUrl: `${req.protocol}://${req.get('host')}/api/license/info`
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to generate demo license', details: error.message });
  }
});

// Demo page
app.get('/demo', (req, res) => {
  res.sendFile(__dirname + '/public/demo.html');
});

// License validation for all protected routes
app.use('/api', licenseMiddleware.validateLicense());
app.use('/api', licenseMiddleware.trackApiUsage());

// License info endpoints
app.get('/api/license/info', licenseMiddleware.getLicenseInfo());
app.get('/api/license/usage', licenseMiddleware.getUsageStats());

// Basic features (available to all tiers)
app.get('/api/users', async (req, res) => {
  try {
    const result = await db.query(
      'SELECT id, email, first_name, last_name, role, status FROM users WHERE customer_id = $1',
      [req.customer.id]
    );
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

app.post('/api/users', licenseMiddleware.checkUserLimits(), async (req, res) => {
  try {
    const { email, firstName, lastName, role = 'user' } = req.body;
    
    const result = await db.query(
      'INSERT INTO users (customer_id, email, first_name, last_name, role) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [req.customer.id, email, firstName, lastName, role]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      res.status(409).json({ error: 'User already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
});

// Professional tier and above features
app.get('/api/analytics/basic', 
  licenseMiddleware.requireFeature('advanced-analytics'),
  async (req, res) => {
    try {
      const stats = await db.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN last_login_at > NOW() - INTERVAL '30 days' THEN 1 END) as recent_users
        FROM users 
        WHERE customer_id = $1
      `, [req.customer.id]);
      
      res.json(stats.rows[0]);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch analytics' });
    }
  }
);

// API access (Professional tier and above)
app.get('/api/external/data',
  licenseMiddleware.requireFeature('api-access'),
  (req, res) => {
    res.json({
      message: 'External API data',
      tier: req.tier,
      timestamp: new Date().toISOString()
    });
  }
);

// Enterprise tier features
app.get('/api/audit-logs',
  licenseMiddleware.requireFeature('audit-logs'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      const offset = (page - 1) * limit;
      
      const result = await db.query(`
        SELECT 
          al.*,
          u.email as user_email
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.customer_id = $1
        ORDER BY al.created_at DESC
        LIMIT $2 OFFSET $3
      `, [req.customer.id, limit, offset]);
      
      res.json({
        logs: result.rows,
        page,
        limit,
        total: result.rowCount
      });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch audit logs' });
    }
  }
);

// SSO configuration (Enterprise tier)
app.get('/api/sso/config',
  licenseMiddleware.requireFeature('sso-integration'),
  async (req, res) => {
    try {
      const result = await db.query(
        'SELECT id, provider, is_active FROM sso_configurations WHERE customer_id = $1',
        [req.customer.id]
      );
      res.json(result.rows);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch SSO configuration' });
    }
  }
);

app.post('/api/sso/config',
  licenseMiddleware.requireFeature('sso-integration'),
  async (req, res) => {
    try {
      const { provider, configuration } = req.body;
      
      const result = await db.query(
        'INSERT INTO sso_configurations (customer_id, provider, configuration) VALUES ($1, $2, $3) RETURNING *',
        [req.customer.id, provider, configuration]
      );
      
      res.status(201).json(result.rows[0]);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create SSO configuration' });
    }
  }
);

// Custom tier features
app.get('/api/white-label/config',
  licenseMiddleware.requireFeature('white-label'),
  async (req, res) => {
    res.json({
      message: 'White-label configuration available',
      tier: req.tier,
      customization: {
        logo: '/custom-logo.png',
        colors: {
          primary: '#your-brand-color',
          secondary: '#your-secondary-color'
        },
        domain: 'your-custom-domain.com'
      }
    });
  }
);

// Admin endpoints for license management
app.post('/admin/license/generate', async (req, res) => {
  // This should be protected with admin authentication
  try {
    const { customerId, tier, expiryDate } = req.body;
    
    const license = licenseMiddleware.licenseManager.generateLicense(
      customerId,
      tier,
      new Date(expiryDate)
    );
    
    // Save to database
    await db.query(
      'INSERT INTO licenses (customer_id, license_key, license_token, tier, expires_at) VALUES ($1, $2, $3, $4, $5)',
      [customerId, license.licenseKey, license.token, tier, expiryDate]
    );
    
    res.json({
      licenseKey: license.licenseKey,
      token: license.token,
      tier,
      expiryDate
    });
  } catch (error) {
    console.error('License generation error:', error);
    res.status(500).json({ error: 'Failed to generate license' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  });
});

app.listen(port, () => {
  console.log(`Enterprise licensing server running on port ${port}`);
});

module.exports = app;
