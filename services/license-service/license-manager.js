const crypto = require('crypto');
const jwt = require('jsonwebtoken');

class LicenseManager {
  constructor(secretKey) {
    this.secretKey = secretKey;
    this.licenseTiers = {
      'starter': {
        maxUsers: 5,
        features: ['basic-features', 'email-support'],
        apiCallsPerDay: 100
      },
      'professional': {
        maxUsers: 50,
        features: ['basic-features', 'advanced-features', 'priority-support', 'api-access', 'custom-branding'],
        apiCallsPerDay: 1000
      },
      'enterprise': {
        maxUsers: -1, // unlimited
        features: ['all-features', 'sso-integration', 'dedicated-support', 'unlimited-api', 'on-premise'],
        apiCallsPerDay: -1 // unlimited
      },
      'custom': {
        maxUsers: -1,
        features: ['all-features', 'white-label', 'custom-integrations', 'sla-guarantees'],
        apiCallsPerDay: -1
      }
    };
  }

  /**
   * Generate a new license key
   */
  generateLicense(customerId, tier, expiryDate, customFeatures = null) {
    const licenseData = {
      customerId,
      tier,
      expiryDate: expiryDate.toISOString(),
      features: customFeatures || this.licenseTiers[tier].features,
      maxUsers: this.licenseTiers[tier].maxUsers,
      apiCallsPerDay: this.licenseTiers[tier].apiCallsPerDay,
      generatedAt: new Date().toISOString()
    };

    // Create JWT token
    const token = jwt.sign(licenseData, this.secretKey, { 
      algorithm: 'HS256',
      expiresIn: Math.floor((expiryDate - new Date()) / 1000) 
    });

    // Create human-readable license key
    const tierCode = tier.substring(0, 3).toUpperCase();
    const expiryCode = expiryDate.toISOString().substring(0, 10).replace(/-/g, '');
    const hash = crypto.createHash('sha256').update(token).digest('hex').substring(0, 8).toUpperCase();
    
    const licenseKey = `MYAPP-${tierCode}-${expiryCode}-${hash}`;

    return {
      licenseKey,
      token,
      data: licenseData
    };
  }

  /**
   * Validate a license key
   */
  validateLicense(licenseKey, token) {
    try {
      // Verify JWT token
      const decoded = jwt.verify(token, this.secretKey);
      
      // Check if license has expired
      if (new Date() > new Date(decoded.expiryDate)) {
        return { valid: false, reason: 'License expired' };
      }

      // Verify license key format matches token
      const tierCode = decoded.tier.substring(0, 3).toUpperCase();
      const expiryCode = new Date(decoded.expiryDate).toISOString().substring(0, 10).replace(/-/g, '');
      const hash = crypto.createHash('sha256').update(token).digest('hex').substring(0, 8).toUpperCase();
      const expectedKey = `MYAPP-${tierCode}-${expiryCode}-${hash}`;

      if (licenseKey !== expectedKey) {
        return { valid: false, reason: 'Invalid license key' };
      }

      return {
        valid: true,
        data: decoded
      };
    } catch (error) {
      return { valid: false, reason: 'Invalid token', error: error.message };
    }
  }

  /**
   * Check if a feature is available for a license tier
   */
  hasFeature(tier, feature) {
    const tierConfig = this.licenseTiers[tier];
    if (!tierConfig) return false;
    
    return tierConfig.features.includes(feature) || 
           tierConfig.features.includes('all-features');
  }

  /**
   * Get usage limits for a tier
   */
  getUsageLimits(tier) {
    return this.licenseTiers[tier] || null;
  }

  /**
   * Check if user count is within license limits
   */
  checkUserLimit(tier, currentUserCount) {
    const limits = this.getUsageLimits(tier);
    if (!limits) return false;
    
    if (limits.maxUsers === -1) return true; // unlimited
    return currentUserCount <= limits.maxUsers;
  }

  /**
   * Check API rate limits
   */
  checkApiLimit(tier, currentApiCalls) {
    const limits = this.getUsageLimits(tier);
    if (!limits) return false;
    
    if (limits.apiCallsPerDay === -1) return true; // unlimited
    return currentApiCalls <= limits.apiCallsPerDay;
  }
}

module.exports = LicenseManager;
