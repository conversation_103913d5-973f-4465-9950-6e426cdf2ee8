class FeatureFlagService {
  constructor() {
    this.featureFlags = {
      // Basic features available to all tiers
      'user-management': ['starter', 'professional', 'enterprise', 'custom'],
      'basic-reporting': ['starter', 'professional', 'enterprise', 'custom'],
      'email-support': ['starter', 'professional', 'enterprise', 'custom'],
      
      // Professional tier and above
      'advanced-analytics': ['professional', 'enterprise', 'custom'],
      'api-access': ['professional', 'enterprise', 'custom'],
      'custom-branding': ['professional', 'enterprise', 'custom'],
      'priority-support': ['professional', 'enterprise', 'custom'],
      'bulk-operations': ['professional', 'enterprise', 'custom'],
      
      // Enterprise tier and above
      'sso-integration': ['enterprise', 'custom'],
      'advanced-security': ['enterprise', 'custom'],
      'audit-logs': ['enterprise', 'custom'],
      'dedicated-support': ['enterprise', 'custom'],
      'on-premise-deployment': ['enterprise', 'custom'],
      'unlimited-api': ['enterprise', 'custom'],
      'team-management': ['enterprise', 'custom'],
      'advanced-permissions': ['enterprise', 'custom'],
      
      // Custom tier only
      'white-label': ['custom'],
      'custom-integrations': ['custom'],
      'sla-guarantees': ['custom'],
      'custom-development': ['custom']
    };

    this.userLimits = {
      'starter': 5,
      'professional': 50,
      'enterprise': -1, // unlimited
      'custom': -1 // unlimited
    };

    this.apiLimits = {
      'starter': 100,
      'professional': 1000,
      'enterprise': -1, // unlimited
      'custom': -1 // unlimited
    };
  }

  /**
   * Check if a feature is enabled for a specific license tier
   */
  isFeatureEnabled(feature, tier) {
    const allowedTiers = this.featureFlags[feature];
    return allowedTiers ? allowedTiers.includes(tier) : false;
  }

  /**
   * Get all enabled features for a tier
   */
  getEnabledFeatures(tier) {
    const enabledFeatures = [];
    
    for (const [feature, allowedTiers] of Object.entries(this.featureFlags)) {
      if (allowedTiers.includes(tier)) {
        enabledFeatures.push(feature);
      }
    }
    
    return enabledFeatures;
  }

  /**
   * Get feature configuration for frontend
   */
  getFeatureConfig(tier) {
    const enabledFeatures = this.getEnabledFeatures(tier);
    
    return {
      tier,
      features: enabledFeatures.reduce((config, feature) => {
        config[feature] = true;
        return config;
      }, {}),
      limits: {
        maxUsers: this.userLimits[tier],
        apiCallsPerDay: this.apiLimits[tier]
      }
    };
  }

  /**
   * Check if user count is within limits
   */
  checkUserLimit(tier, currentUsers) {
    const limit = this.userLimits[tier];
    if (limit === -1) return { allowed: true, limit: 'unlimited' };
    
    return {
      allowed: currentUsers <= limit,
      limit,
      current: currentUsers,
      remaining: Math.max(0, limit - currentUsers)
    };
  }

  /**
   * Check API usage limits
   */
  checkApiLimit(tier, currentCalls) {
    const limit = this.apiLimits[tier];
    if (limit === -1) return { allowed: true, limit: 'unlimited' };
    
    return {
      allowed: currentCalls <= limit,
      limit,
      current: currentCalls,
      remaining: Math.max(0, limit - currentCalls)
    };
  }

  /**
   * Add custom feature flag for specific customer
   */
  addCustomFeature(customerId, feature, enabled = true) {
    // This would typically be stored in database
    // For now, we'll use in-memory storage
    if (!this.customFeatures) {
      this.customFeatures = {};
    }
    
    if (!this.customFeatures[customerId]) {
      this.customFeatures[customerId] = {};
    }
    
    this.customFeatures[customerId][feature] = enabled;
  }

  /**
   * Check if custom feature is enabled for customer
   */
  isCustomFeatureEnabled(customerId, feature) {
    if (!this.customFeatures || !this.customFeatures[customerId]) {
      return false;
    }
    
    return this.customFeatures[customerId][feature] === true;
  }

  /**
   * Get complete feature set for a customer (tier + custom features)
   */
  getCustomerFeatures(customerId, tier) {
    const tierFeatures = this.getEnabledFeatures(tier);
    const customFeatures = this.customFeatures?.[customerId] || {};
    
    const allFeatures = [...tierFeatures];
    
    // Add custom enabled features
    for (const [feature, enabled] of Object.entries(customFeatures)) {
      if (enabled && !allFeatures.includes(feature)) {
        allFeatures.push(feature);
      }
    }
    
    // Remove custom disabled features
    return allFeatures.filter(feature => {
      if (customFeatures.hasOwnProperty(feature)) {
        return customFeatures[feature];
      }
      return true;
    });
  }
}

module.exports = FeatureFlagService;
