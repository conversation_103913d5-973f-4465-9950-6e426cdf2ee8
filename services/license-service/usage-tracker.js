const EventEmitter = require('events');

class UsageTracker extends EventEmitter {
  constructor(database) {
    super();
    this.db = database;
    this.usageCache = new Map(); // In-memory cache for real-time tracking
  }

  /**
   * Track API usage
   */
  async trackApiUsage(customerId, endpoint, method = 'GET') {
    const today = new Date().toISOString().split('T')[0];
    const key = `api:${customerId}:${today}`;
    
    try {
      // Update cache
      const currentCount = this.usageCache.get(key) || 0;
      this.usageCache.set(key, currentCount + 1);
      
      // Update database
      await this.db.query(`
        INSERT INTO usage_tracking (customer_id, usage_type, usage_date, count, metadata)
        VALUES ($1, 'api_calls', $2, 1, $3)
        ON CONFLICT (customer_id, usage_type, usage_date)
        DO UPDATE SET count = usage_tracking.count + 1,
                     metadata = jsonb_set(
                       COALESCE(usage_tracking.metadata, '{}'),
                       '{endpoints}',
                       COALESCE(usage_tracking.metadata->'endpoints', '{}') || $3
                     )
      `, [customerId, today, JSON.stringify({ [endpoint]: { [method]: 1 } })]);
      
      this.emit('apiUsage', { customerId, endpoint, method, count: currentCount + 1 });
      
      return currentCount + 1;
    } catch (error) {
      console.error('Error tracking API usage:', error);
      throw error;
    }
  }

  /**
   * Track user count
   */
  async trackUserCount(customerId, activeUsers) {
    const today = new Date().toISOString().split('T')[0];
    
    try {
      await this.db.query(`
        INSERT INTO usage_tracking (customer_id, usage_type, usage_date, count)
        VALUES ($1, 'active_users', $2, $3)
        ON CONFLICT (customer_id, usage_type, usage_date)
        DO UPDATE SET count = GREATEST(usage_tracking.count, $3)
      `, [customerId, today, activeUsers]);
      
      this.emit('userCount', { customerId, activeUsers });
      
      return activeUsers;
    } catch (error) {
      console.error('Error tracking user count:', error);
      throw error;
    }
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(customerId, feature, metadata = {}) {
    const today = new Date().toISOString().split('T')[0];
    
    try {
      await this.db.query(`
        INSERT INTO usage_tracking (customer_id, usage_type, usage_date, count, metadata)
        VALUES ($1, $2, $3, 1, $4)
        ON CONFLICT (customer_id, usage_type, usage_date)
        DO UPDATE SET count = usage_tracking.count + 1,
                     metadata = usage_tracking.metadata || $4
      `, [customerId, `feature:${feature}`, today, JSON.stringify(metadata)]);
      
      this.emit('featureUsage', { customerId, feature, metadata });
    } catch (error) {
      console.error('Error tracking feature usage:', error);
      throw error;
    }
  }

  /**
   * Get current API usage for today
   */
  async getCurrentApiUsage(customerId) {
    const today = new Date().toISOString().split('T')[0];
    const cacheKey = `api:${customerId}:${today}`;
    
    // Check cache first
    if (this.usageCache.has(cacheKey)) {
      return this.usageCache.get(cacheKey);
    }
    
    try {
      const result = await this.db.query(`
        SELECT count FROM usage_tracking
        WHERE customer_id = $1 AND usage_type = 'api_calls' AND usage_date = $2
      `, [customerId, today]);
      
      const count = result.rows[0]?.count || 0;
      this.usageCache.set(cacheKey, count);
      
      return count;
    } catch (error) {
      console.error('Error getting API usage:', error);
      return 0;
    }
  }

  /**
   * Get usage statistics for a date range
   */
  async getUsageStats(customerId, startDate, endDate) {
    try {
      const result = await this.db.query(`
        SELECT 
          usage_type,
          usage_date,
          count,
          metadata
        FROM usage_tracking
        WHERE customer_id = $1 
          AND usage_date BETWEEN $2 AND $3
        ORDER BY usage_date DESC, usage_type
      `, [customerId, startDate, endDate]);
      
      return result.rows;
    } catch (error) {
      console.error('Error getting usage stats:', error);
      throw error;
    }
  }

  /**
   * Get aggregated usage summary
   */
  async getUsageSummary(customerId, days = 30) {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    try {
      const result = await this.db.query(`
        SELECT 
          usage_type,
          SUM(count) as total_count,
          AVG(count) as avg_daily,
          MAX(count) as peak_daily
        FROM usage_tracking
        WHERE customer_id = $1 
          AND usage_date BETWEEN $2 AND $3
        GROUP BY usage_type
      `, [customerId, startDate, endDate]);
      
      return result.rows.reduce((summary, row) => {
        summary[row.usage_type] = {
          total: parseInt(row.total_count),
          avgDaily: parseFloat(row.avg_daily),
          peakDaily: parseInt(row.peak_daily)
        };
        return summary;
      }, {});
    } catch (error) {
      console.error('Error getting usage summary:', error);
      throw error;
    }
  }

  /**
   * Check if customer is approaching limits
   */
  async checkUsageLimits(customerId, tier, featureFlagService) {
    const currentApiUsage = await this.getCurrentApiUsage(customerId);
    const apiLimitCheck = featureFlagService.checkApiLimit(tier, currentApiUsage);
    
    // Get current user count (you'd implement this based on your user system)
    const currentUsers = await this.getCurrentUserCount(customerId);
    const userLimitCheck = featureFlagService.checkUserLimit(tier, currentUsers);
    
    const warnings = [];
    
    // Check API limits (warn at 80% and 95%)
    if (!apiLimitCheck.allowed) {
      warnings.push({ type: 'api_limit_exceeded', current: currentApiUsage, limit: apiLimitCheck.limit });
    } else if (apiLimitCheck.limit !== 'unlimited') {
      const usage_percentage = (currentApiUsage / apiLimitCheck.limit) * 100;
      if (usage_percentage >= 95) {
        warnings.push({ type: 'api_limit_warning_95', current: currentApiUsage, limit: apiLimitCheck.limit });
      } else if (usage_percentage >= 80) {
        warnings.push({ type: 'api_limit_warning_80', current: currentApiUsage, limit: apiLimitCheck.limit });
      }
    }
    
    // Check user limits
    if (!userLimitCheck.allowed) {
      warnings.push({ type: 'user_limit_exceeded', current: currentUsers, limit: userLimitCheck.limit });
    }
    
    return {
      apiUsage: apiLimitCheck,
      userUsage: userLimitCheck,
      warnings
    };
  }

  /**
   * Get current user count (implement based on your user system)
   */
  async getCurrentUserCount(customerId) {
    try {
      const result = await this.db.query(`
        SELECT COUNT(*) as user_count
        FROM users
        WHERE customer_id = $1 AND status = 'active'
      `, [customerId]);
      
      return parseInt(result.rows[0]?.user_count || 0);
    } catch (error) {
      console.error('Error getting user count:', error);
      return 0;
    }
  }
}

module.exports = UsageTracker;
