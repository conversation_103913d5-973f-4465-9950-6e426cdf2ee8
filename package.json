{"name": "enterprise-licensing-system", "version": "1.0.0", "description": "Enterprise licensing system for SaaS products", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["enterprise", "licensing", "saas", "subscription", "billing"], "author": "Your Company", "license": "MIT", "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.1.4", "express": "^4.18.2", "express-rate-limit": "^6.7.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "passport": "^0.6.0", "passport-oauth2": "^1.7.0", "passport-saml": "^3.2.4", "sqlite3": "^5.1.7", "stripe": "^12.9.0", "winston": "^3.9.0"}, "devDependencies": {"@types/jest": "^29.5.2", "jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}