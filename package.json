{"name": "enterprise-licensing-system", "version": "1.0.0", "description": "Enterprise licensing system for SaaS products", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["enterprise", "licensing", "saas", "subscription", "billing"], "author": "Your Company", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "pg": "^8.11.0", "jsonwebtoken": "^9.0.0", "bcrypt": "^5.1.0", "stripe": "^12.9.0", "passport": "^0.6.0", "passport-saml": "^3.2.4", "passport-oauth2": "^1.7.0", "dotenv": "^16.1.4", "helmet": "^7.0.0", "express-rate-limit": "^6.7.1", "winston": "^3.9.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "@types/jest": "^29.5.2"}, "engines": {"node": ">=16.0.0"}}