# Enterprise Licensing System

A comprehensive enterprise licensing solution for SaaS products with feature flagging, usage tracking, and tier-based access control.

## Features

- **Multi-tier Licensing**: Starter, Professional, Enterprise, and Custom tiers
- **Feature Flagging**: Dynamic feature enabling/disabling based on license tier
- **Usage Tracking**: API calls, user counts, and feature usage monitoring
- **License Validation**: Secure JWT-based license validation
- **Enterprise Features**: SSO, audit logs, white-labeling, custom integrations
- **Client Libraries**: React hooks and JavaScript SDK for easy integration

## Quick Start

### 1. Installation

```bash
npm install
```

### 2. Database Setup

```bash
# Create PostgreSQL database
createdb enterprise_licensing

# Run migrations
psql -d enterprise_licensing -f database/schema.sql
```

### 3. Environment Configuration

Create a `.env` file:

```env
DATABASE_URL=postgresql://localhost:5432/enterprise_licensing
LICENSE_SECRET_KEY=your-super-secret-key-here
PORT=3000
NODE_ENV=development
```

### 4. Start the Server

```bash
npm run dev
```

## License Tiers

### Starter ($29/month)
- Up to 5 users
- Basic features
- Email support
- 100 API calls/day

### Professional ($99/month)
- Up to 50 users
- Advanced analytics
- Priority support
- 1,000 API calls/day
- Custom branding
- API access

### Enterprise ($299/month)
- Unlimited users
- SSO integration
- Audit logs
- Dedicated support
- Unlimited API calls
- Advanced security

### Custom (Contact Sales)
- White-label options
- Custom integrations
- SLA guarantees
- Custom development

## API Usage

### License Validation

All API requests require license headers:

```javascript
const headers = {
  'X-License-Key': 'MYAPP-ENT-20241231-A1B2C3D4',
  'X-License-Token': 'jwt-token-here'
};
```

### Feature Checking

```javascript
// Server-side middleware
app.get('/api/advanced-feature', 
  licenseMiddleware.requireFeature('advanced-analytics'),
  (req, res) => {
    // Feature logic here
  }
);

// Client-side
if (licenseClient.hasFeature('sso-integration')) {
  // Show SSO configuration
}
```

### Usage Tracking

```javascript
// Automatic API tracking
app.use('/api', licenseMiddleware.trackApiUsage());

// Manual feature tracking
await usageTracker.trackFeatureUsage(customerId, 'report-generation');
```

## Client Integration

### React Integration

```jsx
import { LicenseClient, useLicense, FeatureGate } from './client/license-client';

// Initialize client
const licenseClient = new LicenseClient(
  'https://api.yourapp.com',
  'your-license-key',
  'your-license-token'
);

// Use in components
function MyComponent() {
  const { hasFeature, licenseInfo } = useLicense(licenseClient);
  
  return (
    <div>
      <FeatureGate feature="advanced-analytics">
        <AdvancedAnalytics />
      </FeatureGate>
    </div>
  );
}
```

### JavaScript SDK

```javascript
const client = new LicenseClient(apiUrl, licenseKey, token);
await client.initialize();

// Check features
if (client.hasFeature('api-access')) {
  // Enable API features
}

// Get usage stats
const stats = await client.fetchUsageStats();
console.log('API calls today:', stats.current.apiCalls);
```

## License Generation

### Admin API

```javascript
// Generate new license
POST /admin/license/generate
{
  "customerId": "uuid",
  "tier": "enterprise",
  "expiryDate": "2024-12-31"
}

// Response
{
  "licenseKey": "MYAPP-ENT-20241231-A1B2C3D4",
  "token": "jwt-token...",
  "tier": "enterprise",
  "expiryDate": "2024-12-31"
}
```

### Programmatic Generation

```javascript
const licenseManager = new LicenseManager(secretKey);

const license = licenseManager.generateLicense(
  customerId,
  'enterprise',
  new Date('2024-12-31')
);

console.log('License Key:', license.licenseKey);
console.log('Token:', license.token);
```

## Database Schema

Key tables:
- `customers`: Company information and subscription status
- `licenses`: License keys, tokens, and configurations
- `users`: User accounts per customer
- `usage_tracking`: API calls, user counts, feature usage
- `subscriptions`: Billing and subscription management
- `audit_logs`: Enterprise audit trail

## Security Considerations

1. **License Tokens**: Use strong JWT secrets and rotate regularly
2. **API Rate Limiting**: Implement per-tier rate limiting
3. **Audit Logging**: Track all administrative actions
4. **Encryption**: Encrypt sensitive license data at rest
5. **Validation**: Always validate licenses server-side

## Deployment

### Production Environment

```bash
# Set production environment
export NODE_ENV=production
export DATABASE_URL=your-production-db-url
export LICENSE_SECRET_KEY=your-production-secret

# Start with PM2
pm2 start app.js --name enterprise-licensing
```

### Docker Deployment

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## Monitoring and Analytics

### Usage Monitoring

```javascript
// Get customer usage summary
const summary = await usageTracker.getUsageSummary(customerId, 30);

// Check for limit warnings
const limits = await usageTracker.checkUsageLimits(customerId, tier, featureFlags);
```

### Health Checks

```bash
# Health endpoint
GET /health

# License validation test
GET /api/license/info
```

## Support and Maintenance

### Regular Tasks

1. **License Renewal**: Monitor expiring licenses
2. **Usage Analysis**: Review customer usage patterns
3. **Feature Adoption**: Track feature usage by tier
4. **Performance**: Monitor API response times
5. **Security**: Regular security audits

### Troubleshooting

Common issues:
- Invalid license tokens: Check JWT secret and expiry
- Feature access denied: Verify tier permissions
- Rate limit exceeded: Check usage limits and tier
- Database connection: Verify connection string

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
