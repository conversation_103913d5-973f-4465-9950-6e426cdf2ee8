import React from 'react';
import { LicenseClient, useLicense, FeatureGate } from './license-client';

// Initialize license client
const licenseClient = new LicenseClient(
  'https://api.yourapp.com',
  process.env.REACT_APP_LICENSE_KEY,
  process.env.REACT_APP_LICENSE_TOKEN
);

// Main App component with license provider
function App() {
  return (
    <LicenseProvider client={licenseClient}>
      <Dashboard />
    </LicenseProvider>
  );
}

// License Provider Context
const LicenseContext = React.createContext();

function LicenseProvider({ client, children }) {
  const licenseData = useLicense(client);
  
  return (
    <LicenseContext.Provider value={licenseData}>
      {children}
    </LicenseContext.Provider>
  );
}

// Hook to use license context
function useLicenseContext() {
  const context = React.useContext(LicenseContext);
  if (!context) {
    throw new Error('useLicenseContext must be used within LicenseProvider');
  }
  return context;
}

// Dashboard component with feature gates
function Dashboard() {
  const { licenseInfo, usageStats, loading, error } = useLicenseContext();

  if (loading) {
    return <div className="loading">Loading license information...</div>;
  }

  if (error) {
    return <div className="error">License Error: {error}</div>;
  }

  return (
    <div className="dashboard">
      <header>
        <h1>Dashboard</h1>
        <LicenseStatus />
      </header>
      
      <div className="dashboard-content">
        {/* Basic features available to all tiers */}
        <UserManagement />
        
        {/* Professional tier and above */}
        <FeatureGate feature="advanced-analytics">
          <AdvancedAnalytics />
        </FeatureGate>
        
        <FeatureGate feature="api-access">
          <ApiManagement />
        </FeatureGate>
        
        {/* Enterprise tier and above */}
        <FeatureGate feature="sso-integration">
          <SSOConfiguration />
        </FeatureGate>
        
        <FeatureGate feature="audit-logs">
          <AuditLogs />
        </FeatureGate>
        
        {/* Custom tier only */}
        <FeatureGate feature="white-label">
          <WhiteLabelSettings />
        </FeatureGate>
      </div>
    </div>
  );
}

// License status component
function LicenseStatus() {
  const { licenseInfo, usageStats } = useLicenseContext();
  
  const getStatusColor = () => {
    const warnings = usageStats?.limits?.warnings || [];
    if (warnings.length > 0) return 'warning';
    return 'success';
  };

  return (
    <div className={`license-status ${getStatusColor()}`}>
      <div className="license-info">
        <span className="tier">{licenseInfo?.customer?.tier?.toUpperCase()}</span>
        <span className="company">{licenseInfo?.customer?.companyName}</span>
      </div>
      
      <div className="usage-info">
        <UsageIndicator 
          label="API Calls" 
          current={usageStats?.current?.apiCalls || 0}
          limit={usageStats?.limits?.apiUsage?.limit}
        />
        <UsageIndicator 
          label="Users" 
          current={usageStats?.current?.activeUsers || 0}
          limit={usageStats?.limits?.userUsage?.limit}
        />
      </div>
    </div>
  );
}

// Usage indicator component
function UsageIndicator({ label, current, limit }) {
  const percentage = limit === -1 ? 0 : (current / limit) * 100;
  const isUnlimited = limit === -1;
  
  return (
    <div className="usage-indicator">
      <label>{label}</label>
      <div className="usage-bar">
        {!isUnlimited && (
          <div 
            className="usage-fill" 
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        )}
      </div>
      <span className="usage-text">
        {current} {isUnlimited ? '(unlimited)' : `/ ${limit}`}
      </span>
    </div>
  );
}

// Basic user management (available to all tiers)
function UserManagement() {
  const [users, setUsers] = React.useState([]);
  const { hasFeature } = useLicenseContext();
  
  React.useEffect(() => {
    fetchUsers();
  }, []);
  
  const fetchUsers = async () => {
    try {
      const response = await licenseClient.makeRequest('/api/users');
      const userData = await response.json();
      setUsers(userData);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };
  
  return (
    <div className="user-management">
      <h2>User Management</h2>
      <div className="user-list">
        {users.map(user => (
          <div key={user.id} className="user-item">
            <span>{user.first_name} {user.last_name}</span>
            <span>{user.email}</span>
            <span className={`status ${user.status}`}>{user.status}</span>
          </div>
        ))}
      </div>
      
      {hasFeature('team-management') && (
        <button className="btn-primary">Advanced User Management</button>
      )}
    </div>
  );
}

// Advanced analytics (Professional tier+)
function AdvancedAnalytics() {
  const [analytics, setAnalytics] = React.useState(null);
  
  React.useEffect(() => {
    fetchAnalytics();
  }, []);
  
  const fetchAnalytics = async () => {
    try {
      const response = await licenseClient.makeRequest('/api/analytics/basic');
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    }
  };
  
  return (
    <div className="advanced-analytics">
      <h2>Advanced Analytics</h2>
      {analytics && (
        <div className="analytics-grid">
          <div className="metric">
            <h3>Total Users</h3>
            <span className="value">{analytics.total_users}</span>
          </div>
          <div className="metric">
            <h3>Active Users</h3>
            <span className="value">{analytics.active_users}</span>
          </div>
          <div className="metric">
            <h3>Recent Users</h3>
            <span className="value">{analytics.recent_users}</span>
          </div>
        </div>
      )}
    </div>
  );
}

// API Management (Professional tier+)
function ApiManagement() {
  return (
    <div className="api-management">
      <h2>API Management</h2>
      <div className="api-info">
        <p>Your API credentials and usage statistics.</p>
        <button className="btn-secondary">Generate API Key</button>
        <button className="btn-secondary">View Documentation</button>
      </div>
    </div>
  );
}

// SSO Configuration (Enterprise tier+)
function SSOConfiguration() {
  const [ssoConfigs, setSsoConfigs] = React.useState([]);
  
  React.useEffect(() => {
    fetchSSOConfigs();
  }, []);
  
  const fetchSSOConfigs = async () => {
    try {
      const response = await licenseClient.makeRequest('/api/sso/config');
      const data = await response.json();
      setSsoConfigs(data);
    } catch (error) {
      console.error('Failed to fetch SSO configs:', error);
    }
  };
  
  return (
    <div className="sso-configuration">
      <h2>SSO Configuration</h2>
      <div className="sso-list">
        {ssoConfigs.map(config => (
          <div key={config.id} className="sso-item">
            <span>{config.provider.toUpperCase()}</span>
            <span className={`status ${config.is_active ? 'active' : 'inactive'}`}>
              {config.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        ))}
      </div>
      <button className="btn-primary">Add SSO Provider</button>
    </div>
  );
}

// Audit Logs (Enterprise tier+)
function AuditLogs() {
  const [logs, setLogs] = React.useState([]);
  
  React.useEffect(() => {
    fetchAuditLogs();
  }, []);
  
  const fetchAuditLogs = async () => {
    try {
      const response = await licenseClient.makeRequest('/api/audit-logs');
      const data = await response.json();
      setLogs(data.logs || []);
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
    }
  };
  
  return (
    <div className="audit-logs">
      <h2>Audit Logs</h2>
      <div className="logs-list">
        {logs.map(log => (
          <div key={log.id} className="log-item">
            <span className="timestamp">{new Date(log.created_at).toLocaleString()}</span>
            <span className="action">{log.action}</span>
            <span className="user">{log.user_email || 'System'}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

// White Label Settings (Custom tier only)
function WhiteLabelSettings() {
  return (
    <div className="white-label-settings">
      <h2>White Label Configuration</h2>
      <div className="settings-form">
        <div className="form-group">
          <label>Custom Logo</label>
          <input type="file" accept="image/*" />
        </div>
        <div className="form-group">
          <label>Primary Color</label>
          <input type="color" defaultValue="#007bff" />
        </div>
        <div className="form-group">
          <label>Custom Domain</label>
          <input type="text" placeholder="your-domain.com" />
        </div>
        <button className="btn-primary">Save Configuration</button>
      </div>
    </div>
  );
}

export default App;
