/**
 * Client-side license management and feature flag system
 */
class LicenseClient {
  constructor(apiBaseUrl, licenseKey, licenseToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.licenseKey = licenseKey;
    this.licenseToken = licenseToken;
    this.licenseInfo = null;
    this.features = {};
    this.limits = {};
    this.usageStats = null;
  }

  /**
   * Initialize the license client
   */
  async initialize() {
    try {
      await this.fetchLicenseInfo();
      await this.fetchUsageStats();
      return true;
    } catch (error) {
      console.error('Failed to initialize license client:', error);
      return false;
    }
  }

  /**
   * Fetch license information from the server
   */
  async fetchLicenseInfo() {
    const response = await this.makeRequest('/api/license/info');
    if (response.ok) {
      const data = await response.json();
      this.licenseInfo = data;
      this.features = data.features || {};
      this.limits = data.limits || {};
      
      // Emit event for UI updates
      this.emit('licenseLoaded', data);
      return data;
    } else {
      throw new Error('Failed to fetch license info');
    }
  }

  /**
   * Fetch current usage statistics
   */
  async fetchUsageStats() {
    const response = await this.makeRequest('/api/license/usage');
    if (response.ok) {
      const data = await response.json();
      this.usageStats = data;
      
      // Check for warnings
      if (data.limits && data.limits.warnings.length > 0) {
        this.emit('usageWarning', data.limits.warnings);
      }
      
      return data;
    } else {
      throw new Error('Failed to fetch usage stats');
    }
  }

  /**
   * Check if a feature is enabled
   */
  hasFeature(feature) {
    return this.features[feature] === true;
  }

  /**
   * Get feature availability with upgrade information
   */
  getFeatureInfo(feature) {
    const isEnabled = this.hasFeature(feature);
    
    if (isEnabled) {
      return { enabled: true, tier: this.licenseInfo?.customer?.tier };
    }

    // Determine which tiers have this feature
    const availableInTiers = this.getFeatureAvailability(feature);
    
    return {
      enabled: false,
      availableInTiers,
      upgradeRequired: true,
      currentTier: this.licenseInfo?.customer?.tier
    };
  }

  /**
   * Get which tiers support a feature (this would come from server)
   */
  getFeatureAvailability(feature) {
    const featureMap = {
      'advanced-analytics': ['professional', 'enterprise', 'custom'],
      'api-access': ['professional', 'enterprise', 'custom'],
      'custom-branding': ['professional', 'enterprise', 'custom'],
      'sso-integration': ['enterprise', 'custom'],
      'audit-logs': ['enterprise', 'custom'],
      'white-label': ['custom']
    };
    
    return featureMap[feature] || [];
  }

  /**
   * Check usage limits
   */
  checkUsageLimits() {
    if (!this.usageStats) return null;
    
    const { current, limits } = this.usageStats;
    const warnings = [];
    
    // Check API limits
    if (limits.apiUsage && !limits.apiUsage.allowed) {
      warnings.push({
        type: 'api_limit_exceeded',
        message: 'API rate limit exceeded',
        current: current.apiCalls,
        limit: limits.apiUsage.limit
      });
    }
    
    // Check user limits
    if (limits.userUsage && !limits.userUsage.allowed) {
      warnings.push({
        type: 'user_limit_exceeded',
        message: 'User limit exceeded',
        current: current.activeUsers,
        limit: limits.userUsage.limit
      });
    }
    
    return warnings;
  }

  /**
   * Make authenticated API request
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.apiBaseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'X-License-Key': this.licenseKey,
      'X-License-Token': this.licenseToken,
      ...options.headers
    };

    return fetch(url, {
      ...options,
      headers
    });
  }

  /**
   * Simple event emitter for license events
   */
  emit(event, data) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.listeners) this.listeners = {};
    if (!this.listeners[event]) this.listeners[event] = [];
    this.listeners[event].push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }
}

/**
 * React Hook for license management
 */
function useLicense(licenseClient) {
  const [licenseInfo, setLicenseInfo] = React.useState(null);
  const [usageStats, setUsageStats] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    const initializeLicense = async () => {
      try {
        setLoading(true);
        const success = await licenseClient.initialize();
        if (success) {
          setLicenseInfo(licenseClient.licenseInfo);
          setUsageStats(licenseClient.usageStats);
        } else {
          setError('Failed to initialize license');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    initializeLicense();

    // Listen for license updates
    const handleLicenseLoaded = (data) => {
      setLicenseInfo(data);
    };

    const handleUsageWarning = (warnings) => {
      console.warn('Usage warnings:', warnings);
      // You could show notifications here
    };

    licenseClient.on('licenseLoaded', handleLicenseLoaded);
    licenseClient.on('usageWarning', handleUsageWarning);

    return () => {
      licenseClient.off('licenseLoaded', handleLicenseLoaded);
      licenseClient.off('usageWarning', handleUsageWarning);
    };
  }, [licenseClient]);

  const hasFeature = React.useCallback((feature) => {
    return licenseClient.hasFeature(feature);
  }, [licenseClient]);

  const getFeatureInfo = React.useCallback((feature) => {
    return licenseClient.getFeatureInfo(feature);
  }, [licenseClient]);

  const refreshUsage = React.useCallback(async () => {
    try {
      const stats = await licenseClient.fetchUsageStats();
      setUsageStats(stats);
      return stats;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [licenseClient]);

  return {
    licenseInfo,
    usageStats,
    loading,
    error,
    hasFeature,
    getFeatureInfo,
    refreshUsage
  };
}

/**
 * React component for feature gating
 */
function FeatureGate({ feature, children, fallback = null, showUpgrade = true }) {
  const { hasFeature, getFeatureInfo } = useLicense();
  
  if (hasFeature(feature)) {
    return children;
  }
  
  if (showUpgrade) {
    const featureInfo = getFeatureInfo(feature);
    return (
      <div className="feature-upgrade-prompt">
        <p>This feature requires an upgrade to: {featureInfo.availableInTiers.join(', ')}</p>
        <button onClick={() => window.open('/upgrade', '_blank')}>
          Upgrade Now
        </button>
      </div>
    );
  }
  
  return fallback;
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { LicenseClient, useLicense, FeatureGate };
} else if (typeof window !== 'undefined') {
  window.LicenseClient = LicenseClient;
  window.useLicense = useLicense;
  window.FeatureGate = FeatureGate;
}
