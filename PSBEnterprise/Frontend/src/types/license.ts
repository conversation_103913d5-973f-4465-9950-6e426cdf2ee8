export interface LicenseInfo {
  customer: CustomerInfo;
  license: LicenseDetails;
  features: Record<string, boolean>;
  limits: LicenseLimits;
}

export interface CustomerInfo {
  id: string;
  companyName: string;
  tier: LicenseTier;
  subscriptionStatus: string;
}

export interface LicenseDetails {
  tier: LicenseTier;
  expiryDate: string;
  maxUsers: number;
  apiCallsPerDay: number;
  isActive: boolean;
}

export interface LicenseLimits {
  maxUsers: number;
  apiCallsPerDay: number;
}

export interface UsageStats {
  current: CurrentUsage;
  limits: UsageLimits;
  summary: UsageSummary;
  period: string;
}

export interface CurrentUsage {
  apiCalls: number;
  activeUsers: number;
}

export interface UsageLimits {
  apiUsage: UsageLimit;
  userUsage: UsageLimit;
  warnings: string[];
}

export interface UsageLimit {
  allowed: boolean;
  limit: number;
  current: number;
  remaining: number;
}

export interface UsageSummary {
  metrics: Record<string, UsageMetric>;
}

export interface UsageMetric {
  total: number;
  avgDaily: number;
  peakDaily: number;
}

export interface FeatureCheckResult {
  feature: string;
  enabled: boolean;
  customerId: string;
}

export interface LicenseValidationRequest {
  licenseKey: string;
  licenseToken: string;
}

export interface LicenseValidationResult {
  isValid: boolean;
  customerId: string;
  tier: LicenseTier;
  expiryDate: string;
  features: string[];
  maxUsers: number;
  apiCallsPerDay: number;
  errorMessage?: string;
}

export type LicenseTier = 'starter' | 'professional' | 'enterprise' | 'custom';

export interface TierInfo {
  name: string;
  price: number;
  maxUsers: number;
  apiCallsPerDay: number;
  features: string[];
  description: string;
  popular?: boolean;
}

export const TIER_INFO: Record<LicenseTier, TierInfo> = {
  starter: {
    name: 'Starter',
    price: 29,
    maxUsers: 5,
    apiCallsPerDay: 100,
    features: ['user-management', 'basic-reporting', 'email-support'],
    description: 'Perfect for small teams getting started'
  },
  professional: {
    name: 'Professional',
    price: 99,
    maxUsers: 50,
    apiCallsPerDay: 1000,
    features: [
      'user-management',
      'basic-reporting',
      'email-support',
      'advanced-analytics',
      'api-access',
      'custom-branding',
      'priority-support'
    ],
    description: 'Ideal for growing businesses',
    popular: true
  },
  enterprise: {
    name: 'Enterprise',
    price: 299,
    maxUsers: -1,
    apiCallsPerDay: -1,
    features: [
      'all-features',
      'sso-integration',
      'audit-logs',
      'advanced-security',
      'dedicated-support',
      'unlimited-api',
      'team-management'
    ],
    description: 'Comprehensive solution for large organizations'
  },
  custom: {
    name: 'Custom',
    price: 0,
    maxUsers: -1,
    apiCallsPerDay: -1,
    features: [
      'all-features',
      'white-label',
      'custom-integrations',
      'sla-guarantees',
      'custom-development'
    ],
    description: 'Tailored solutions for unique requirements'
  }
};

export interface User {
  id: string;
  customerId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: string;
  status: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserRequest {
  customerId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

export interface UpdateUserRequest {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  status?: string;
}

export interface Customer {
  id: string;
  companyName: string;
  contactEmail: string;
  billingEmail?: string;
  phone?: string;
  address?: string;
  subscriptionStatus: string;
  trialEndsAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerRequest {
  companyName: string;
  contactEmail: string;
  billingEmail?: string;
  phone?: string;
  address?: string;
  subscriptionStatus?: string;
  trialEndsAt?: string;
}

export interface CustomerStats {
  totalUsers: number;
  activeUsers: number;
  apiCallsToday: number;
  currentTier: string;
  licenseExpiryDate?: string;
  featureUsage: Record<string, number>;
}

export interface AuditLog {
  id: string;
  customerId: string;
  userId?: string;
  action: string;
  resourceType?: string;
  resourceId?: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

export interface SystemAnalytics {
  totalCustomers: number;
  activeLicenses: number;
  totalUsers: number;
  apiCallsToday: number;
  tierDistribution: Record<string, number>;
  revenueMetrics: RevenueMetrics;
}

export interface RevenueMetrics {
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  averageRevenuePerUser: number;
  churnRate: number;
}
