import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  LicenseInfo,
  UsageStats,
  FeatureCheckResult,
  LicenseValidationRequest,
  LicenseValidationResult,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  Customer,
  CreateCustomerRequest,
  CustomerStats,
  AuditLog,
  SystemAnalytics
} from '@/types/license';

class ApiService {
  private api: AxiosInstance;
  private licenseKey: string | null = null;
  private licenseToken: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_BASE_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add license headers
    this.api.interceptors.request.use(
      (config) => {
        if (this.licenseKey && this.licenseToken) {
          config.headers['X-License-Key'] = this.licenseKey;
          config.headers['X-License-Token'] = this.licenseToken;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle license validation errors
          console.error('License validation failed:', error.response.data);
        }
        return Promise.reject(error);
      }
    );
  }

  // Set license credentials
  setLicenseCredentials(licenseKey: string, licenseToken: string): void {
    this.licenseKey = licenseKey;
    this.licenseToken = licenseToken;
  }

  // Clear license credentials
  clearLicenseCredentials(): void {
    this.licenseKey = null;
    this.licenseToken = null;
  }

  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.api.request(config);
    return response.data;
  }

  // License endpoints
  async getLicenseInfo(): Promise<LicenseInfo> {
    return this.request<LicenseInfo>({
      method: 'GET',
      url: '/license/info',
    });
  }

  async getUsageStats(days: number = 30): Promise<UsageStats> {
    return this.request<UsageStats>({
      method: 'GET',
      url: '/license/usage',
      params: { days },
    });
  }

  async validateLicense(request: LicenseValidationRequest): Promise<LicenseValidationResult> {
    return this.request<LicenseValidationResult>({
      method: 'POST',
      url: '/license/validate',
      data: request,
    });
  }

  async checkFeature(feature: string): Promise<FeatureCheckResult> {
    return this.request<FeatureCheckResult>({
      method: 'GET',
      url: `/license/features/${feature}`,
    });
  }

  // User endpoints
  async getUsers(): Promise<User[]> {
    return this.request<User[]>({
      method: 'GET',
      url: '/users',
    });
  }

  async createUser(request: CreateUserRequest): Promise<User> {
    return this.request<User>({
      method: 'POST',
      url: '/users',
      data: request,
    });
  }

  async updateUser(userId: string, request: UpdateUserRequest): Promise<User> {
    return this.request<User>({
      method: 'PUT',
      url: `/users/${userId}`,
      data: request,
    });
  }

  async deleteUser(userId: string): Promise<void> {
    return this.request<void>({
      method: 'DELETE',
      url: `/users/${userId}`,
    });
  }

  async activateUser(userId: string): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: `/users/${userId}/activate`,
    });
  }

  async deactivateUser(userId: string): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: `/users/${userId}/deactivate`,
    });
  }

  // Analytics endpoints (Professional+)
  async getBasicAnalytics(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/analytics/basic',
    });
  }

  // SSO endpoints (Enterprise+)
  async getSSOConfigurations(): Promise<any[]> {
    return this.request<any[]>({
      method: 'GET',
      url: '/sso/config',
    });
  }

  async createSSOConfiguration(config: any): Promise<any> {
    return this.request<any>({
      method: 'POST',
      url: '/sso/config',
      data: config,
    });
  }

  // Audit logs endpoints (Enterprise+)
  async getAuditLogs(page: number = 1, pageSize: number = 50): Promise<{ auditLogs: AuditLog[]; totalCount: number }> {
    return this.request<{ auditLogs: AuditLog[]; totalCount: number }>({
      method: 'GET',
      url: '/audit-logs',
      params: { page, pageSize },
    });
  }

  // Admin endpoints
  async generateLicense(request: any): Promise<any> {
    return this.request<any>({
      method: 'POST',
      url: '/admin/license/generate',
      data: request,
    });
  }

  async getCustomers(page: number = 1, pageSize: number = 50): Promise<{ customers: Customer[]; totalCount: number }> {
    return this.request<{ customers: Customer[]; totalCount: number }>({
      method: 'GET',
      url: '/admin/customers',
      params: { page, pageSize },
    });
  }

  async createCustomer(request: CreateCustomerRequest): Promise<Customer> {
    return this.request<Customer>({
      method: 'POST',
      url: '/admin/customers',
      data: request,
    });
  }

  async getCustomer(customerId: string): Promise<Customer> {
    return this.request<Customer>({
      method: 'GET',
      url: `/admin/customers/${customerId}`,
    });
  }

  async getCustomerStats(customerId: string): Promise<CustomerStats> {
    return this.request<CustomerStats>({
      method: 'GET',
      url: `/admin/customers/${customerId}/stats`,
    });
  }

  async upgradeCustomer(customerId: string, newTier: string): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: `/admin/customers/${customerId}/upgrade`,
      data: { newTier },
    });
  }

  async getSystemAnalytics(): Promise<SystemAnalytics> {
    return this.request<SystemAnalytics>({
      method: 'GET',
      url: '/admin/analytics',
    });
  }

  async getAdminAuditLogs(
    customerId?: string,
    action?: string,
    page: number = 1,
    pageSize: number = 50
  ): Promise<{ auditLogs: AuditLog[]; totalCount: number }> {
    return this.request<{ auditLogs: AuditLog[]; totalCount: number }>({
      method: 'GET',
      url: '/admin/audit-logs',
      params: { customerId, action, page, pageSize },
    });
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/health',
    });
  }

  // Demo endpoints (development only)
  async generateDemoLicense(tier: string = 'enterprise'): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/demo/generate-license',
      params: { tier },
    });
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;
