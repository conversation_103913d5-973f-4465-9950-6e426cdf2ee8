import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import {
  LicenseInfo,
  UsageStats,
  FeatureCheckResult,
  LicenseValidationRequest,
  LicenseValidationResult,
} from '@/types/license';

// License info hook
export const useLicenseInfo = () => {
  return useQuery<LicenseInfo, Error>({
    queryKey: ['license', 'info'],
    queryFn: () => apiService.getLicenseInfo(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Usage stats hook
export const useUsageStats = (days: number = 30) => {
  return useQuery<UsageStats, Error>({
    queryKey: ['license', 'usage', days],
    queryFn: () => apiService.getUsageStats(days),
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
  });
};

// Feature check hook
export const useFeatureCheck = (feature: string) => {
  return useQuery<FeatureCheckResult, Error>({
    queryKey: ['license', 'feature', feature],
    queryFn: () => apiService.checkFeature(feature),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    enabled: !!feature,
  });
};

// License validation hook
export const useLicenseValidation = () => {
  return useMutation<LicenseValidationResult, Error, LicenseValidationRequest>({
    mutationFn: (request) => apiService.validateLicense(request),
  });
};

// Combined license hook for easy access to all license data
export const useLicense = () => {
  const queryClient = useQueryClient();
  
  const licenseInfo = useLicenseInfo();
  const usageStats = useUsageStats();
  
  const hasFeature = (feature: string): boolean => {
    return licenseInfo.data?.features[feature] === true;
  };

  const getFeatureInfo = (feature: string) => {
    const enabled = hasFeature(feature);
    const tier = licenseInfo.data?.customer.tier;
    
    return {
      enabled,
      tier,
      upgradeRequired: !enabled,
    };
  };

  const refreshLicenseData = () => {
    queryClient.invalidateQueries({ queryKey: ['license'] });
  };

  const isLoading = licenseInfo.isLoading || usageStats.isLoading;
  const error = licenseInfo.error || usageStats.error;

  return {
    licenseInfo: licenseInfo.data,
    usageStats: usageStats.data,
    isLoading,
    error,
    hasFeature,
    getFeatureInfo,
    refreshLicenseData,
  };
};

// License context for setting credentials
import React, { createContext, useContext, useState, useEffect } from 'react';

interface LicenseContextType {
  licenseKey: string | null;
  licenseToken: string | null;
  setLicenseCredentials: (key: string, token: string) => void;
  clearLicenseCredentials: () => void;
  isAuthenticated: boolean;
}

const LicenseContext = createContext<LicenseContextType | undefined>(undefined);

export const LicenseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [licenseKey, setLicenseKey] = useState<string | null>(null);
  const [licenseToken, setLicenseToken] = useState<string | null>(null);

  // Load credentials from localStorage on mount
  useEffect(() => {
    const savedKey = localStorage.getItem('psb_license_key');
    const savedToken = localStorage.getItem('psb_license_token');
    
    if (savedKey && savedToken) {
      setLicenseKey(savedKey);
      setLicenseToken(savedToken);
      apiService.setLicenseCredentials(savedKey, savedToken);
    }
  }, []);

  const setLicenseCredentials = (key: string, token: string) => {
    setLicenseKey(key);
    setLicenseToken(token);
    
    // Save to localStorage
    localStorage.setItem('psb_license_key', key);
    localStorage.setItem('psb_license_token', token);
    
    // Set in API service
    apiService.setLicenseCredentials(key, token);
  };

  const clearLicenseCredentials = () => {
    setLicenseKey(null);
    setLicenseToken(null);
    
    // Remove from localStorage
    localStorage.removeItem('psb_license_key');
    localStorage.removeItem('psb_license_token');
    
    // Clear from API service
    apiService.clearLicenseCredentials();
  };

  const isAuthenticated = !!(licenseKey && licenseToken);

  return (
    <LicenseContext.Provider
      value={{
        licenseKey,
        licenseToken,
        setLicenseCredentials,
        clearLicenseCredentials,
        isAuthenticated,
      }}
    >
      {children}
    </LicenseContext.Provider>
  );
};

export const useLicenseContext = () => {
  const context = useContext(LicenseContext);
  if (context === undefined) {
    throw new Error('useLicenseContext must be used within a LicenseProvider');
  }
  return context;
};

// Feature gate hook
export const useFeatureGate = (feature: string) => {
  const { hasFeature, getFeatureInfo } = useLicense();
  
  return {
    enabled: hasFeature(feature),
    info: getFeatureInfo(feature),
  };
};
