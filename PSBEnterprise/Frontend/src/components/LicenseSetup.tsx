import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Container,
  Paper,
  Divider,
} from '@mui/material';
import { Security as SecurityIcon } from '@mui/icons-material';
import { useLicenseContext } from '@/hooks/useLicense';
import { apiService } from '@/services/api';

export const LicenseSetup: React.FC = () => {
  const navigate = useNavigate();
  const { setLicenseCredentials } = useLicenseContext();
  const [licenseKey, setLicenseKey] = useState('');
  const [licenseToken, setLicenseToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [demoLoading, setDemoLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate the license
      const validation = await apiService.validateLicense({
        licenseKey,
        licenseToken,
      });

      if (validation.isValid) {
        // Set credentials and navigate to dashboard
        setLicenseCredentials(licenseKey, licenseToken);
        navigate('/dashboard');
      } else {
        setError(validation.errorMessage || 'Invalid license credentials');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to validate license');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateDemo = async () => {
    setDemoLoading(true);
    setError(null);

    try {
      const demoLicense = await apiService.generateDemoLicense('enterprise');
      
      if (demoLicense.success) {
        setLicenseKey(demoLicense.licenseKey);
        setLicenseToken(demoLicense.licenseToken);
        setError(null);
      } else {
        setError(demoLicense.errorMessage || 'Failed to generate demo license');
      }
    } catch (err: any) {
      setError('Failed to generate demo license. Please check if the API is running.');
    } finally {
      setDemoLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4,
        }}
      >
        <Card sx={{ width: '100%', maxWidth: 600 }}>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <SecurityIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                PSB Enterprise Licensing
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Enter your license credentials to access the system
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="License Key"
                value={licenseKey}
                onChange={(e) => setLicenseKey(e.target.value)}
                placeholder="PSB-ENT-20241231-A1B2C3D4"
                margin="normal"
                required
                disabled={loading}
              />
              
              <TextField
                fullWidth
                label="License Token"
                value={licenseToken}
                onChange={(e) => setLicenseToken(e.target.value)}
                placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                margin="normal"
                multiline
                rows={4}
                required
                disabled={loading}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading || !licenseKey || !licenseToken}
                sx={{ mt: 3, mb: 2 }}
              >
                {loading ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Validating License...
                  </>
                ) : (
                  'Activate License'
                )}
              </Button>
            </Box>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                OR
              </Typography>
            </Divider>

            <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
              <Typography variant="h6" gutterBottom>
                Demo Mode
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Generate a demo Enterprise license for testing purposes. This will create
                a temporary license with full Enterprise features.
              </Typography>
              <Button
                fullWidth
                variant="outlined"
                onClick={handleGenerateDemo}
                disabled={demoLoading}
              >
                {demoLoading ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Generating Demo License...
                  </>
                ) : (
                  'Generate Demo License'
                )}
              </Button>
            </Paper>

            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                Need help? Contact <NAME_EMAIL>
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default LicenseSetup;
