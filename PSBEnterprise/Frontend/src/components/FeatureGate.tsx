import React from 'react';
import { Box, Paper, Typography, <PERSON>ton, Chip } from '@mui/material';
import { Lock as LockIcon, Upgrade as UpgradeIcon } from '@mui/icons-material';
import { useFeatureGate } from '@/hooks/useLicense';
import { TIER_INFO, LicenseTier } from '@/types/license';

interface FeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback,
  showUpgradePrompt = true,
}) => {
  const { enabled, info } = useFeatureGate(feature);

  if (enabled) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return <UpgradePrompt feature={feature} currentTier={info.tier} />;
  }

  return null;
};

interface UpgradePromptProps {
  feature: string;
  currentTier?: LicenseTier;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ feature, currentTier }) => {
  // Find which tiers include this feature
  const availableTiers = Object.entries(TIER_INFO)
    .filter(([_, tierInfo]) => tierInfo.features.includes(feature) || tierInfo.features.includes('all-features'))
    .map(([tier, tierInfo]) => ({ tier: tier as LicenseTier, ...tierInfo }));

  const nextTier = availableTiers.find(tier => 
    currentTier && getTierLevel(tier.tier) > getTierLevel(currentTier)
  );

  const handleUpgrade = () => {
    // Navigate to upgrade page or open upgrade modal
    window.open('/upgrade', '_blank');
  };

  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        textAlign: 'center',
        backgroundColor: 'grey.50',
        border: '2px dashed',
        borderColor: 'grey.300',
      }}
    >
      <Box sx={{ mb: 2 }}>
        <LockIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
        <Typography variant="h6" gutterBottom>
          Feature Not Available
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          The "{formatFeatureName(feature)}" feature is not included in your current plan.
        </Typography>
      </Box>

      {nextTier && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Available in:
          </Typography>
          <Chip
            label={`${nextTier.name} - $${nextTier.price}/month`}
            color="primary"
            variant="outlined"
            sx={{ mb: 2 }}
          />
          <Box>
            <Button
              variant="contained"
              startIcon={<UpgradeIcon />}
              onClick={handleUpgrade}
              size="small"
            >
              Upgrade to {nextTier.name}
            </Button>
          </Box>
        </Box>
      )}

      {availableTiers.length > 1 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Also available in: {availableTiers.slice(1).map(t => t.name).join(', ')}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

// Helper function to get tier level for comparison
const getTierLevel = (tier: LicenseTier): number => {
  const levels = { starter: 1, professional: 2, enterprise: 3, custom: 4 };
  return levels[tier] || 0;
};

// Helper function to format feature names
const formatFeatureName = (feature: string): string => {
  return feature
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Inline feature gate for smaller components
interface InlineFeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const InlineFeatureGate: React.FC<InlineFeatureGateProps> = ({
  feature,
  children,
  fallback,
}) => {
  const { enabled } = useFeatureGate(feature);

  if (enabled) {
    return <>{children}</>;
  }

  return fallback ? <>{fallback}</> : null;
};

// Feature badge component
interface FeatureBadgeProps {
  feature: string;
  children: React.ReactNode;
}

export const FeatureBadge: React.FC<FeatureBadgeProps> = ({ feature, children }) => {
  const { enabled } = useFeatureGate(feature);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      {children}
      {!enabled && (
        <Chip
          label="Pro"
          size="small"
          color="primary"
          sx={{
            position: 'absolute',
            top: -8,
            right: -8,
            fontSize: '0.7rem',
            height: 16,
          }}
        />
      )}
    </Box>
  );
};

export default FeatureGate;
