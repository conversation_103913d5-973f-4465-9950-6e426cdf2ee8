import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  People as PeopleIcon,
  Api as ApiIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useLicense } from '@/hooks/useLicense';
import { FeatureGate } from './FeatureGate';
import { TIER_INFO } from '@/types/license';
import { format } from 'date-fns';

export const Dashboard: React.FC = () => {
  const { licenseInfo, usageStats, isLoading, error } = useLicense();

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load license information: {error.message}
      </Alert>
    );
  }

  if (!licenseInfo) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        No license information available
      </Alert>
    );
  }

  const tierInfo = TIER_INFO[licenseInfo.customer.tier];
  const isUnlimited = (value: number) => value === -1;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Welcome, {licenseInfo.customer.companyName}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Chip
            label={`${tierInfo.name} Plan`}
            color="primary"
            variant={tierInfo.popular ? 'filled' : 'outlined'}
          />
          <Chip
            label={licenseInfo.customer.subscriptionStatus}
            color={licenseInfo.customer.subscriptionStatus === 'active' ? 'success' : 'warning'}
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary">
          License expires: {format(new Date(licenseInfo.license.expiryDate), 'PPP')}
        </Typography>
      </Box>

      {/* Usage Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PeopleIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">User Usage</Typography>
              </Box>
              {usageStats && (
                <>
                  <Typography variant="h4" gutterBottom>
                    {usageStats.current.activeUsers}
                    {!isUnlimited(licenseInfo.limits.maxUsers) && (
                      <Typography component="span" variant="body1" color="text.secondary">
                        /{licenseInfo.limits.maxUsers}
                      </Typography>
                    )}
                  </Typography>
                  {!isUnlimited(licenseInfo.limits.maxUsers) && (
                    <LinearProgress
                      variant="determinate"
                      value={(usageStats.current.activeUsers / licenseInfo.limits.maxUsers) * 100}
                      sx={{ mb: 1 }}
                    />
                  )}
                  <Typography variant="body2" color="text.secondary">
                    {isUnlimited(licenseInfo.limits.maxUsers) ? 'Unlimited users' : 'Active users'}
                  </Typography>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ApiIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">API Usage</Typography>
              </Box>
              {usageStats && (
                <>
                  <Typography variant="h4" gutterBottom>
                    {usageStats.current.apiCalls}
                    {!isUnlimited(licenseInfo.limits.apiCallsPerDay) && (
                      <Typography component="span" variant="body1" color="text.secondary">
                        /{licenseInfo.limits.apiCallsPerDay}
                      </Typography>
                    )}
                  </Typography>
                  {!isUnlimited(licenseInfo.limits.apiCallsPerDay) && (
                    <LinearProgress
                      variant="determinate"
                      value={(usageStats.current.apiCalls / licenseInfo.limits.apiCallsPerDay) * 100}
                      sx={{ mb: 1 }}
                    />
                  )}
                  <Typography variant="body2" color="text.secondary">
                    {isUnlimited(licenseInfo.limits.apiCallsPerDay) ? 'Unlimited API calls' : 'Calls today'}
                  </Typography>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Feature Cards */}
      <Grid container spacing={3}>
        {/* Basic Features */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Management
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Manage your team members and their access levels.
              </Typography>
              <Chip label="Available" color="success" size="small" />
            </CardContent>
          </Card>
        </Grid>

        {/* Professional Features */}
        <Grid item xs={12} md={4}>
          <FeatureGate feature="advanced-analytics">
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Advanced Analytics</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Detailed insights and reporting for your business.
                </Typography>
                <Chip label="Available" color="success" size="small" />
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>

        {/* Enterprise Features */}
        <Grid item xs={12} md={4}>
          <FeatureGate feature="sso-integration">
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SecurityIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">SSO Integration</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Single Sign-On with your identity provider.
                </Typography>
                <Chip label="Available" color="success" size="small" />
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>

        {/* Audit Logs */}
        <Grid item xs={12} md={4}>
          <FeatureGate feature="audit-logs">
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Audit Logs
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Complete activity tracking and compliance reporting.
                </Typography>
                <Chip label="Available" color="success" size="small" />
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>

        {/* White Label */}
        <Grid item xs={12} md={4}>
          <FeatureGate feature="white-label">
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  White Label
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Complete branding customization for your organization.
                </Typography>
                <Chip label="Available" color="success" size="small" />
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>

        {/* Custom Integrations */}
        <Grid item xs={12} md={4}>
          <FeatureGate feature="custom-integrations">
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Custom Integrations
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Tailored integrations with your existing systems.
                </Typography>
                <Chip label="Available" color="success" size="small" />
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>
      </Grid>

      {/* Usage Warnings */}
      {usageStats?.limits.warnings && usageStats.limits.warnings.length > 0 && (
        <Box sx={{ mt: 4 }}>
          {usageStats.limits.warnings.map((warning, index) => (
            <Alert key={index} severity="warning" sx={{ mb: 1 }}>
              {warning}
            </Alert>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default Dashboard;
