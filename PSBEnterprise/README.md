# PSB Enterprise Licensing System

## Project Overview

PSB Enterprise is a comprehensive licensing system built with:
- **Frontend**: React 18 with TypeScript
- **Backend**: .NET 8 Web API with C#
- **Database**: Oracle Database
- **Authentication**: JWT-based licensing system

## Project Structure

```
PSBEnterprise/
├── README.md
├── docker-compose.yml
├── .gitignore
├── Backend/
│   ├── PSBEnterprise.API/          # Web API project
│   ├── PSBEnterprise.Core/         # Business logic and entities
│   ├── PSBEnterprise.Infrastructure/ # Data access and external services
│   ├── PSBEnterprise.Tests/        # Unit and integration tests
│   └── PSBEnterprise.sln           # Solution file
├── Frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── services/
│   │   ├── hooks/
│   │   ├── types/
│   │   └── utils/
│   ├── package.json
│   └── tsconfig.json
├── Database/
│   ├── Scripts/
│   │   ├── 001_CreateTables.sql
│   │   ├── 002_InsertSeedData.sql
│   │   └── 003_CreateIndexes.sql
│   └── Migrations/
└── Documentation/
    ├── API/
    ├── Database/
    └── Deployment/
```

## Quick Start

### Automated Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd PSBEnterprise

# Run the automated setup script
./setup.sh

# Follow the prompts to:
# 1. Install dependencies
# 2. Build the application
# 3. Start all services with Docker
```

### Manual Setup

#### Prerequisites
- .NET 8 SDK
- Node.js 18+
- Oracle Database 19c+ (or Docker for Oracle XE)
- Docker & Docker Compose (recommended)

#### Option 1: Docker Setup (Recommended)
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

#### Option 2: Local Development Setup
```bash
# Backend setup
cd Backend
dotnet restore
dotnet build
dotnet run --project PSBEnterprise.API

# Frontend setup (in new terminal)
cd Frontend
npm install
npm start

# Database setup (Oracle)
sqlplus system/password@localhost:1521/XE
@Database/Scripts/001_CreateTables.sql
@Database/Scripts/002_InsertSeedData.sql
@Database/Scripts/003_CreateIndexes.sql
```

### Access the Application
- **Frontend**: http://localhost:3000
- **API**: https://localhost:7001
- **Swagger UI**: https://localhost:7001/swagger
- **Health Check**: https://localhost:7001/health

## License Tiers

| Tier | Price | Users | API Calls | Features |
|------|-------|-------|-----------|----------|
| Starter | $29/month | 5 | 100/day | Basic features |
| Professional | $99/month | 50 | 1,000/day | Advanced analytics, API access |
| Enterprise | $299/month | Unlimited | Unlimited | SSO, Audit logs |
| Custom | Contact Sales | Unlimited | Unlimited | White-label, Custom features |

## Technology Stack

### Backend (.NET 8)
- **ASP.NET Core Web API** - RESTful API
- **Entity Framework Core** - ORM for Oracle
- **JWT Authentication** - Secure token-based auth
- **AutoMapper** - Object mapping
- **FluentValidation** - Input validation
- **Serilog** - Structured logging
- **Swagger/OpenAPI** - API documentation

### Frontend (React)
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Material-UI (MUI)** - Component library
- **React Query** - Data fetching and caching
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Hook Form** - Form management

### Database (Oracle)
- **Oracle Database 19c+** - Primary database
- **Entity Framework Core Oracle Provider** - Database access
- **Oracle Data Access Components (ODAC)** - Oracle connectivity

## Key Features

### License Management
- Secure JWT-based license validation
- Multi-tier licensing with feature flags
- Usage tracking and limits enforcement
- Automatic license renewal

### Feature Flagging
- Dynamic feature enabling/disabling
- Tier-based feature access
- Custom feature overrides
- A/B testing support

### Enterprise Features
- Single Sign-On (SSO) integration
- Comprehensive audit logging
- Advanced security controls
- White-label customization

### Analytics & Monitoring
- Real-time usage tracking
- Performance metrics
- Business intelligence dashboards
- Automated alerting

## Development

### Running Locally
```bash
# Start backend
cd Backend
dotnet run --project PSBEnterprise.API

# Start frontend (in new terminal)
cd Frontend
npm start

# Access application
# Frontend: http://localhost:3000
# Backend API: https://localhost:7001
# Swagger UI: https://localhost:7001/swagger
```

### Testing
```bash
# Backend tests
cd Backend
dotnet test

# Frontend tests
cd Frontend
npm test
```

### Building for Production
```bash
# Backend
cd Backend
dotnet publish -c Release -o ./publish

# Frontend
cd Frontend
npm run build
```

## Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d

# Scale services
docker-compose up -d --scale api=3
```

### Cloud Deployment
- **Azure**: App Service + Azure SQL Database
- **AWS**: ECS + RDS Oracle
- **On-Premise**: IIS + Oracle Database

## Configuration

### Backend Configuration (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=localhost:1521/XE;User Id=psb_user;Password=password;"
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key-here",
    "Issuer": "PSBEnterprise",
    "Audience": "PSBEnterprise.API",
    "ExpirationMinutes": 60
  },
  "LicenseSettings": {
    "SecretKey": "your-license-secret-key",
    "DefaultExpirationDays": 365
  }
}
```

### Frontend Configuration (.env)
```bash
REACT_APP_API_BASE_URL=https://localhost:7001/api
REACT_APP_ENVIRONMENT=development
REACT_APP_VERSION=1.0.0
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - User logout

### License Management
- `GET /api/license/info` - Get license information
- `GET /api/license/usage` - Get usage statistics
- `POST /api/license/validate` - Validate license

### User Management
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Admin
- `POST /api/admin/license/generate` - Generate license
- `GET /api/admin/customers` - List customers
- `GET /api/admin/analytics` - System analytics

## Security

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- License-based feature access
- API rate limiting

### Data Protection
- Encryption at rest (Oracle TDE)
- Encryption in transit (HTTPS/TLS)
- PII data encryption
- Audit logging

### Compliance
- GDPR compliance tools
- SOC 2 controls
- Data retention policies
- Security monitoring

## Monitoring

### Application Monitoring
- Health checks
- Performance metrics
- Error tracking
- Usage analytics

### Infrastructure Monitoring
- Database performance
- API response times
- Resource utilization
- Security events

## Support

### Documentation
- API documentation (Swagger)
- User manuals
- Developer guides
- Deployment guides

### Getting Help
- GitHub Issues
- Email support
- Documentation wiki
- Community forum

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

---

**PSB Enterprise Licensing System** - Secure, scalable, and feature-rich licensing solution for enterprise applications.
