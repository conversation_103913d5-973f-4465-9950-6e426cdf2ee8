version: '3.8'

services:
  # Oracle Database
  oracle-db:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: psb-oracle-db
    environment:
      - ORACLE_PWD=OraclePassword123
      - ORACLE_CHARACTERSET=AL32UTF8
    ports:
      - "1521:1521"
      - "5500:5500"
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./Database/Scripts:/opt/oracle/scripts/setup
    networks:
      - psb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/OraclePassword123@//localhost:1521/XE", "@/opt/oracle/scripts/setup/healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5

  # .NET API Backend
  psb-api:
    build:
      context: ./Backend
      dockerfile: PSBEnterprise.API/Dockerfile
    container_name: psb-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=PSBEnterprise2024
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
      - ConnectionStrings__DefaultConnection=Data Source=oracle-db:1521/XE;User Id=psb_user;Password=psb_password;Pooling=true;Connection Timeout=60;
      - JwtSettings__SecretKey=PSBEnterprise-Super-Secret-Key-For-JWT-Tokens-2024
      - LicenseSettings__SecretKey=PSBEnterprise-License-Secret-Key-For-Validation-2024
    ports:
      - "7001:443"
      - "7000:80"
    volumes:
      - ~/.aspnet/https:/https:ro
      - ./logs:/app/logs
    depends_on:
      oracle-db:
        condition: service_healthy
    networks:
      - psb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  psb-frontend:
    build:
      context: ./Frontend
      dockerfile: Dockerfile
    container_name: psb-frontend
    environment:
      - REACT_APP_API_BASE_URL=https://localhost:7001/api
      - REACT_APP_ENVIRONMENT=production
    ports:
      - "3000:80"
    depends_on:
      - psb-api
    networks:
      - psb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (optional)
  redis:
    image: redis:7-alpine
    container_name: psb-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - psb-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: psb-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - psb-frontend
      - psb-api
    networks:
      - psb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: psb-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - psb-network
    restart: unless-stopped

  # Grafana Dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: psb-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=PSBEnterprise2024
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - psb-network
    restart: unless-stopped

volumes:
  oracle_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  psb-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml
version: '3.8'

services:
  oracle-db:
    environment:
      - ORACLE_PWD=DevPassword123
    volumes:
      - ./Database/Scripts:/docker-entrypoint-initdb.d

  psb-api:
    build:
      context: ./Backend
      dockerfile: PSBEnterprise.API/Dockerfile.dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Data Source=oracle-db:1521/XE;User Id=psb_user;Password=dev_password;
    volumes:
      - ./Backend:/app
      - /app/bin
      - /app/obj
    command: ["dotnet", "watch", "run", "--project", "PSBEnterprise.API"]

  psb-frontend:
    build:
      context: ./Frontend
      dockerfile: Dockerfile.dev
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:7000/api
      - REACT_APP_ENVIRONMENT=development
    volumes:
      - ./Frontend:/app
      - /app/node_modules
    command: ["npm", "start"]

  # Development database initialization
  db-init:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: psb-db-init
    environment:
      - ORACLE_PWD=DevPassword123
    volumes:
      - ./Database/Scripts:/scripts
    depends_on:
      oracle-db:
        condition: service_healthy
    networks:
      - psb-network
    command: >
      bash -c "
        echo 'Waiting for Oracle to be ready...'
        sleep 30
        echo 'Creating PSB user and schema...'
        sqlplus system/DevPassword123@oracle-db:1521/XE @/scripts/create_user.sql
        echo 'Creating tables...'
        sqlplus psb_user/dev_password@oracle-db:1521/XE @/scripts/001_CreateTables.sql
        echo 'Inserting seed data...'
        sqlplus psb_user/dev_password@oracle-db:1521/XE @/scripts/002_InsertSeedData.sql
        echo 'Creating indexes...'
        sqlplus psb_user/dev_password@oracle-db:1521/XE @/scripts/003_CreateIndexes.sql
        echo 'Database initialization complete!'
      "
    restart: "no"
