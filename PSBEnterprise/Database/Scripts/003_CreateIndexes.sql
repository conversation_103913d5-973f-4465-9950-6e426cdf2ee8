-- PSB Enterprise Licensing System - Performance Indexes
-- Version: 1.0
-- Created: 2024-07-11

-- Performance indexes for optimal query performance

-- CUSTOMERS table indexes
CREATE INDEX IDX_CUSTOMERS_EMAIL ON CUSTOMERS(CONTACT_EMAIL);
CREATE INDEX IDX_CUSTOMERS_STATUS ON CUSTOMERS(SUBSCRIPTION_STATUS);
CREATE INDEX IDX_CUSTOMERS_CREATED_AT ON CUSTOMERS(CREATED_AT);
CREATE INDEX IDX_CUSTOMERS_COMPANY_NAME ON CUSTOMERS(UPPER(COMPANY_NAME));

-- LICENSES table indexes
CREATE INDEX IDX_LICENSES_CUSTOMER_ID ON LICENSES(CUSTOMER_ID);
CREATE INDEX IDX_LICENSES_KEY ON LICENSES(LICENSE_KEY);
CREATE INDEX IDX_LICENSES_TIER ON LICENSES(TIER);
CREATE INDEX IDX_LICENSES_ACTIVE ON LICENSES(IS_ACTIVE);
CREATE INDEX IDX_LICENSES_EXPIRES_AT ON LICENSES(EXPIRES_AT);
CREATE INDEX IDX_LICENSES_CUSTOMER_ACTIVE ON LICENSES(CUSTOMER_ID, IS_ACTIVE);

-- USERS table indexes
CREATE INDEX IDX_USERS_CUSTOMER_ID ON USERS(CUSTOMER_ID);
CREATE INDEX IDX_USERS_EMAIL ON USERS(EMAIL);
CREATE INDEX IDX_USERS_STATUS ON USERS(STATUS);
CREATE INDEX IDX_USERS_ROLE ON USERS(ROLE);
CREATE INDEX IDX_USERS_CUSTOMER_STATUS ON USERS(CUSTOMER_ID, STATUS);
CREATE INDEX IDX_USERS_LAST_LOGIN ON USERS(LAST_LOGIN_AT);

-- USAGE_TRACKING table indexes
CREATE INDEX IDX_USAGE_CUSTOMER_ID ON USAGE_TRACKING(CUSTOMER_ID);
CREATE INDEX IDX_USAGE_TYPE ON USAGE_TRACKING(USAGE_TYPE);
CREATE INDEX IDX_USAGE_DATE ON USAGE_TRACKING(USAGE_DATE);
CREATE INDEX IDX_USAGE_CUSTOMER_DATE ON USAGE_TRACKING(CUSTOMER_ID, USAGE_DATE);
CREATE INDEX IDX_USAGE_CUSTOMER_TYPE ON USAGE_TRACKING(CUSTOMER_ID, USAGE_TYPE);
CREATE INDEX IDX_USAGE_TYPE_DATE ON USAGE_TRACKING(USAGE_TYPE, USAGE_DATE);

-- SUBSCRIPTIONS table indexes
CREATE INDEX IDX_SUBSCRIPTIONS_CUSTOMER_ID ON SUBSCRIPTIONS(CUSTOMER_ID);
CREATE INDEX IDX_SUBSCRIPTIONS_STRIPE_ID ON SUBSCRIPTIONS(STRIPE_SUBSCRIPTION_ID);
CREATE INDEX IDX_SUBSCRIPTIONS_STATUS ON SUBSCRIPTIONS(STATUS);
CREATE INDEX IDX_SUBSCRIPTIONS_TIER ON SUBSCRIPTIONS(TIER);
CREATE INDEX IDX_SUBSCRIPTIONS_PERIOD_END ON SUBSCRIPTIONS(CURRENT_PERIOD_END);
CREATE INDEX IDX_SUBSCRIPTIONS_CUSTOMER_STATUS ON SUBSCRIPTIONS(CUSTOMER_ID, STATUS);

-- INVOICES table indexes
CREATE INDEX IDX_INVOICES_CUSTOMER_ID ON INVOICES(CUSTOMER_ID);
CREATE INDEX IDX_INVOICES_SUBSCRIPTION_ID ON INVOICES(SUBSCRIPTION_ID);
CREATE INDEX IDX_INVOICES_STRIPE_ID ON INVOICES(STRIPE_INVOICE_ID);
CREATE INDEX IDX_INVOICES_STATUS ON INVOICES(STATUS);
CREATE INDEX IDX_INVOICES_DUE_DATE ON INVOICES(DUE_DATE);
CREATE INDEX IDX_INVOICES_CREATED_AT ON INVOICES(CREATED_AT);
CREATE INDEX IDX_INVOICES_CUSTOMER_STATUS ON INVOICES(CUSTOMER_ID, STATUS);

-- AUDIT_LOGS table indexes
CREATE INDEX IDX_AUDIT_CUSTOMER_ID ON AUDIT_LOGS(CUSTOMER_ID);
CREATE INDEX IDX_AUDIT_USER_ID ON AUDIT_LOGS(USER_ID);
CREATE INDEX IDX_AUDIT_ACTION ON AUDIT_LOGS(ACTION);
CREATE INDEX IDX_AUDIT_RESOURCE_TYPE ON AUDIT_LOGS(RESOURCE_TYPE);
CREATE INDEX IDX_AUDIT_CREATED_AT ON AUDIT_LOGS(CREATED_AT);
CREATE INDEX IDX_AUDIT_CUSTOMER_ACTION ON AUDIT_LOGS(CUSTOMER_ID, ACTION);
CREATE INDEX IDX_AUDIT_CUSTOMER_DATE ON AUDIT_LOGS(CUSTOMER_ID, CREATED_AT);
CREATE INDEX IDX_AUDIT_ACTION_DATE ON AUDIT_LOGS(ACTION, CREATED_AT);

-- Composite indexes for common query patterns
CREATE INDEX IDX_LICENSES_CUSTOMER_TIER_ACTIVE ON LICENSES(CUSTOMER_ID, TIER, IS_ACTIVE);
CREATE INDEX IDX_USERS_CUSTOMER_ROLE_STATUS ON USERS(CUSTOMER_ID, ROLE, STATUS);
CREATE INDEX IDX_USAGE_CUSTOMER_TYPE_DATE ON USAGE_TRACKING(CUSTOMER_ID, USAGE_TYPE, USAGE_DATE);

-- Function-based indexes for case-insensitive searches
CREATE INDEX IDX_CUSTOMERS_COMPANY_UPPER ON CUSTOMERS(UPPER(COMPANY_NAME));
CREATE INDEX IDX_CUSTOMERS_EMAIL_UPPER ON CUSTOMERS(UPPER(CONTACT_EMAIL));
CREATE INDEX IDX_USERS_EMAIL_UPPER ON USERS(UPPER(EMAIL));

-- Bitmap indexes for low-cardinality columns (if using Enterprise Edition)
-- CREATE BITMAP INDEX BMP_LICENSES_TIER ON LICENSES(TIER);
-- CREATE BITMAP INDEX BMP_LICENSES_ACTIVE ON LICENSES(IS_ACTIVE);
-- CREATE BITMAP INDEX BMP_USERS_STATUS ON USERS(STATUS);
-- CREATE BITMAP INDEX BMP_USERS_ROLE ON USERS(ROLE);
-- CREATE BITMAP INDEX BMP_SUBSCRIPTIONS_STATUS ON SUBSCRIPTIONS(STATUS);
-- CREATE BITMAP INDEX BMP_INVOICES_STATUS ON INVOICES(STATUS);

-- Partitioned indexes (if tables are partitioned)
-- These would be created automatically if tables are partitioned by date

-- Statistics gathering for better query optimization
BEGIN
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'CUSTOMERS',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'LICENSES',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'USERS',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'USAGE_TRACKING',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'SUBSCRIPTIONS',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'INVOICES',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'AUDIT_LOGS',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
END;
/

-- Create stored procedures for common operations
CREATE OR REPLACE PROCEDURE SP_GET_CUSTOMER_USAGE_STATS(
    p_customer_id IN RAW,
    p_start_date IN DATE DEFAULT SYSDATE - 30,
    p_end_date IN DATE DEFAULT SYSDATE,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
        SELECT 
            USAGE_TYPE,
            SUM(COUNT) as TOTAL_COUNT,
            AVG(COUNT) as AVG_DAILY,
            MAX(COUNT) as PEAK_DAILY,
            MIN(USAGE_DATE) as FIRST_DATE,
            MAX(USAGE_DATE) as LAST_DATE
        FROM USAGE_TRACKING
        WHERE CUSTOMER_ID = p_customer_id
        AND USAGE_DATE BETWEEN p_start_date AND p_end_date
        GROUP BY USAGE_TYPE
        ORDER BY USAGE_TYPE;
END;
/

CREATE OR REPLACE PROCEDURE SP_GET_LICENSE_INFO(
    p_license_key IN VARCHAR2,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
        SELECT 
            l.ID as LICENSE_ID,
            l.CUSTOMER_ID,
            c.COMPANY_NAME,
            c.CONTACT_EMAIL,
            c.SUBSCRIPTION_STATUS,
            l.LICENSE_KEY,
            l.TIER,
            l.MAX_USERS,
            l.API_CALLS_PER_DAY,
            l.FEATURES,
            l.CUSTOM_FEATURES,
            l.EXPIRES_AT,
            l.IS_ACTIVE,
            l.CREATED_AT
        FROM LICENSES l
        JOIN CUSTOMERS c ON l.CUSTOMER_ID = c.ID
        WHERE l.LICENSE_KEY = p_license_key
        AND l.IS_ACTIVE = 1;
END;
/

CREATE OR REPLACE FUNCTION FN_CHECK_USER_LIMIT(
    p_customer_id IN RAW,
    p_max_users IN NUMBER
) RETURN NUMBER AS
    v_current_users NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO v_current_users
    FROM USERS
    WHERE CUSTOMER_ID = p_customer_id
    AND STATUS = 'active';
    
    IF p_max_users = -1 THEN
        RETURN 1; -- Unlimited
    ELSIF v_current_users < p_max_users THEN
        RETURN 1; -- Within limit
    ELSE
        RETURN 0; -- Limit exceeded
    END IF;
END;
/

CREATE OR REPLACE FUNCTION FN_CHECK_API_LIMIT(
    p_customer_id IN RAW,
    p_api_calls_per_day IN NUMBER
) RETURN NUMBER AS
    v_current_calls NUMBER;
BEGIN
    SELECT NVL(SUM(COUNT), 0)
    INTO v_current_calls
    FROM USAGE_TRACKING
    WHERE CUSTOMER_ID = p_customer_id
    AND USAGE_TYPE = 'api_calls'
    AND USAGE_DATE = TRUNC(SYSDATE);
    
    IF p_api_calls_per_day = -1 THEN
        RETURN 1; -- Unlimited
    ELSIF v_current_calls < p_api_calls_per_day THEN
        RETURN 1; -- Within limit
    ELSE
        RETURN 0; -- Limit exceeded
    END IF;
END;
/

-- Create materialized view for reporting (if needed)
-- CREATE MATERIALIZED VIEW MV_CUSTOMER_DAILY_STATS
-- BUILD IMMEDIATE
-- REFRESH FAST ON COMMIT
-- AS
-- SELECT 
--     c.ID as CUSTOMER_ID,
--     c.COMPANY_NAME,
--     l.TIER,
--     TRUNC(SYSDATE) as REPORT_DATE,
--     COUNT(u.ID) as TOTAL_USERS,
--     SUM(CASE WHEN u.STATUS = 'active' THEN 1 ELSE 0 END) as ACTIVE_USERS,
--     NVL(ut.API_CALLS, 0) as API_CALLS_TODAY
-- FROM CUSTOMERS c
-- LEFT JOIN LICENSES l ON c.ID = l.CUSTOMER_ID AND l.IS_ACTIVE = 1
-- LEFT JOIN USERS u ON c.ID = u.CUSTOMER_ID
-- LEFT JOIN (
--     SELECT CUSTOMER_ID, SUM(COUNT) as API_CALLS
--     FROM USAGE_TRACKING
--     WHERE USAGE_TYPE = 'api_calls'
--     AND USAGE_DATE = TRUNC(SYSDATE)
--     GROUP BY CUSTOMER_ID
-- ) ut ON c.ID = ut.CUSTOMER_ID
-- GROUP BY c.ID, c.COMPANY_NAME, l.TIER;

COMMIT;

-- Display index creation summary
SELECT 'Indexes created successfully' as STATUS FROM DUAL;

-- Show index information
SELECT 
    INDEX_NAME,
    TABLE_NAME,
    UNIQUENESS,
    STATUS
FROM USER_INDEXES
WHERE TABLE_NAME IN (
    'CUSTOMERS', 'LICENSES', 'USERS', 'USAGE_TRACKING', 
    'SUBSCRIPTIONS', 'INVOICES', 'AUDIT_LOGS'
)
ORDER BY TABLE_NAME, INDEX_NAME;
