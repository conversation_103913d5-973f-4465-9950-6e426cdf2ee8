-- PSB Enterprise Licensing System - Oracle Database Schema
-- Version: 1.0
-- Created: 2024-07-11

-- Create tablespace for PSB Enterprise (optional)
-- CREATE TABLESPACE PSB_ENTERPRISE_DATA
-- DATAFILE 'psb_enterprise_data.dbf' SIZE 100M
-- AUTOEXTEND ON NEXT 10M MAXSIZE 1G;

-- Create user and grant privileges
-- CREATE USER psb_user IDENTIFIED BY psb_password
-- DEFAULT TABLESPACE PSB_ENTERPRISE_DATA
-- TEMPORARY TABLESPACE TEMP;

-- GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE TO psb_user;
-- GRANT UNLIMITED TABLESPACE TO psb_user;

-- Connect as psb_user before running the following scripts

-- Drop tables if they exist (for clean reinstall)
BEGIN
    FOR c IN (SELECT table_name FROM user_tables WHERE table_name IN (
        'AUDIT_LOGS', 'INVOICES', 'SUBSCRIPTIONS', 'USAGE_TRACKING', 
        'USERS', 'LICENSES', 'CUSTOMERS'
    )) LOOP
        EXECUTE IMMEDIATE 'DROP TABLE ' || c.table_name || ' CASCADE CONSTRAINTS';
    END LOOP;
END;
/

-- Create CUSTOMERS table
CREATE TABLE CUSTOMERS (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    COMPANY_NAME VARCHAR2(255) NOT NULL,
    CONTACT_EMAIL VARCHAR2(255) NOT NULL,
    BILLING_EMAIL VARCHAR2(255),
    PHONE VARCHAR2(50),
    ADDRESS CLOB,
    SUBSCRIPTION_STATUS VARCHAR2(20) DEFAULT 'active' NOT NULL,
    TRIAL_ENDS_AT TIMESTAMP,
    METADATA CLOB,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT UK_CUSTOMERS_EMAIL UNIQUE (CONTACT_EMAIL),
    CONSTRAINT CK_CUSTOMERS_STATUS CHECK (SUBSCRIPTION_STATUS IN ('active', 'suspended', 'cancelled', 'trial'))
);

-- Create LICENSES table
CREATE TABLE LICENSES (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    LICENSE_KEY VARCHAR2(255) NOT NULL,
    LICENSE_TOKEN CLOB NOT NULL,
    TIER VARCHAR2(20) NOT NULL,
    MAX_USERS NUMBER DEFAULT -1,
    FEATURES CLOB,
    CUSTOM_FEATURES CLOB,
    API_CALLS_PER_DAY NUMBER DEFAULT -1,
    EXPIRES_AT TIMESTAMP NOT NULL,
    IS_ACTIVE NUMBER(1) DEFAULT 1 NOT NULL,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_LICENSES_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT UK_LICENSES_KEY UNIQUE (LICENSE_KEY),
    CONSTRAINT CK_LICENSES_TIER CHECK (TIER IN ('starter', 'professional', 'enterprise', 'custom')),
    CONSTRAINT CK_LICENSES_ACTIVE CHECK (IS_ACTIVE IN (0, 1))
);

-- Create USERS table
CREATE TABLE USERS (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    EMAIL VARCHAR2(255) NOT NULL,
    FIRST_NAME VARCHAR2(100),
    LAST_NAME VARCHAR2(100),
    ROLE VARCHAR2(50) DEFAULT 'user' NOT NULL,
    STATUS VARCHAR2(20) DEFAULT 'active' NOT NULL,
    LAST_LOGIN_AT TIMESTAMP,
    PASSWORD_HASH CLOB,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_USERS_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT UK_USERS_CUSTOMER_EMAIL UNIQUE (CUSTOMER_ID, EMAIL),
    CONSTRAINT CK_USERS_ROLE CHECK (ROLE IN ('user', 'admin', 'manager', 'super_admin')),
    CONSTRAINT CK_USERS_STATUS CHECK (STATUS IN ('active', 'inactive', 'suspended', 'pending'))
);

-- Create USAGE_TRACKING table
CREATE TABLE USAGE_TRACKING (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    USAGE_TYPE VARCHAR2(50) NOT NULL,
    USAGE_DATE DATE NOT NULL,
    COUNT NUMBER DEFAULT 0,
    METADATA CLOB,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_USAGE_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT UK_USAGE_CUSTOMER_TYPE_DATE UNIQUE (CUSTOMER_ID, USAGE_TYPE, USAGE_DATE),
    CONSTRAINT CK_USAGE_TYPE CHECK (USAGE_TYPE IN ('api_calls', 'active_users', 'feature_usage', 'data_storage', 'bandwidth'))
);

-- Create SUBSCRIPTIONS table
CREATE TABLE SUBSCRIPTIONS (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    STRIPE_SUBSCRIPTION_ID VARCHAR2(255),
    TIER VARCHAR2(20) NOT NULL,
    STATUS VARCHAR2(20) NOT NULL,
    CURRENT_PERIOD_START TIMESTAMP NOT NULL,
    CURRENT_PERIOD_END TIMESTAMP NOT NULL,
    CANCEL_AT_PERIOD_END NUMBER(1) DEFAULT 0 NOT NULL,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_SUBSCRIPTIONS_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT UK_SUBSCRIPTIONS_STRIPE UNIQUE (STRIPE_SUBSCRIPTION_ID),
    CONSTRAINT CK_SUBSCRIPTIONS_TIER CHECK (TIER IN ('starter', 'professional', 'enterprise', 'custom')),
    CONSTRAINT CK_SUBSCRIPTIONS_STATUS CHECK (STATUS IN ('active', 'past_due', 'canceled', 'unpaid')),
    CONSTRAINT CK_SUBSCRIPTIONS_CANCEL CHECK (CANCEL_AT_PERIOD_END IN (0, 1))
);

-- Create INVOICES table
CREATE TABLE INVOICES (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    SUBSCRIPTION_ID RAW(16),
    STRIPE_INVOICE_ID VARCHAR2(255),
    AMOUNT_DUE NUMBER NOT NULL,
    AMOUNT_PAID NUMBER DEFAULT 0,
    CURRENCY VARCHAR2(3) DEFAULT 'USD' NOT NULL,
    STATUS VARCHAR2(20) NOT NULL,
    DUE_DATE TIMESTAMP,
    PAID_AT TIMESTAMP,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_INVOICES_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT FK_INVOICES_SUBSCRIPTION FOREIGN KEY (SUBSCRIPTION_ID) REFERENCES SUBSCRIPTIONS(ID) ON DELETE SET NULL,
    CONSTRAINT UK_INVOICES_STRIPE UNIQUE (STRIPE_INVOICE_ID),
    CONSTRAINT CK_INVOICES_STATUS CHECK (STATUS IN ('draft', 'open', 'paid', 'void', 'uncollectible'))
);

-- Create AUDIT_LOGS table
CREATE TABLE AUDIT_LOGS (
    ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
    CUSTOMER_ID RAW(16) NOT NULL,
    USER_ID RAW(16),
    ACTION VARCHAR2(100) NOT NULL,
    RESOURCE_TYPE VARCHAR2(50),
    RESOURCE_ID VARCHAR2(255),
    DETAILS CLOB,
    IP_ADDRESS VARCHAR2(45),
    USER_AGENT CLOB,
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    CONSTRAINT FK_AUDIT_CUSTOMER FOREIGN KEY (CUSTOMER_ID) REFERENCES CUSTOMERS(ID) ON DELETE CASCADE,
    CONSTRAINT FK_AUDIT_USER FOREIGN KEY (USER_ID) REFERENCES USERS(ID) ON DELETE SET NULL
);

-- Create sequences for numeric IDs (if needed)
-- CREATE SEQUENCE SEQ_CUSTOMERS START WITH 1 INCREMENT BY 1;
-- CREATE SEQUENCE SEQ_LICENSES START WITH 1 INCREMENT BY 1;
-- CREATE SEQUENCE SEQ_USERS START WITH 1 INCREMENT BY 1;

-- Create triggers for updating UPDATED_AT timestamps
CREATE OR REPLACE TRIGGER TRG_CUSTOMERS_UPDATED_AT
    BEFORE UPDATE ON CUSTOMERS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_LICENSES_UPDATED_AT
    BEFORE UPDATE ON LICENSES
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_USERS_UPDATED_AT
    BEFORE UPDATE ON USERS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_USAGE_UPDATED_AT
    BEFORE UPDATE ON USAGE_TRACKING
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_SUBSCRIPTIONS_UPDATED_AT
    BEFORE UPDATE ON SUBSCRIPTIONS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

-- Create views for easier querying
CREATE OR REPLACE VIEW V_CUSTOMER_LICENSES AS
SELECT 
    c.ID as CUSTOMER_ID,
    c.COMPANY_NAME,
    c.CONTACT_EMAIL,
    c.SUBSCRIPTION_STATUS,
    l.ID as LICENSE_ID,
    l.LICENSE_KEY,
    l.TIER,
    l.MAX_USERS,
    l.API_CALLS_PER_DAY,
    l.EXPIRES_AT,
    l.IS_ACTIVE as LICENSE_ACTIVE,
    l.CREATED_AT as LICENSE_CREATED_AT
FROM CUSTOMERS c
LEFT JOIN LICENSES l ON c.ID = l.CUSTOMER_ID AND l.IS_ACTIVE = 1;

CREATE OR REPLACE VIEW V_CUSTOMER_USAGE_SUMMARY AS
SELECT 
    c.ID as CUSTOMER_ID,
    c.COMPANY_NAME,
    COUNT(u.ID) as TOTAL_USERS,
    SUM(CASE WHEN u.STATUS = 'active' THEN 1 ELSE 0 END) as ACTIVE_USERS,
    l.TIER,
    l.MAX_USERS,
    l.API_CALLS_PER_DAY
FROM CUSTOMERS c
LEFT JOIN USERS u ON c.ID = u.CUSTOMER_ID
LEFT JOIN LICENSES l ON c.ID = l.CUSTOMER_ID AND l.IS_ACTIVE = 1
GROUP BY c.ID, c.COMPANY_NAME, l.TIER, l.MAX_USERS, l.API_CALLS_PER_DAY;

-- Grant permissions on views
-- GRANT SELECT ON V_CUSTOMER_LICENSES TO PUBLIC;
-- GRANT SELECT ON V_CUSTOMER_USAGE_SUMMARY TO PUBLIC;

COMMIT;

-- Display table creation summary
SELECT 'Tables created successfully' as STATUS FROM DUAL;

-- Show table counts
SELECT 
    'CUSTOMERS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM CUSTOMERS
UNION ALL
SELECT 
    'LICENSES' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM LICENSES
UNION ALL
SELECT 
    'USERS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM USERS
UNION ALL
SELECT 
    'USAGE_TRACKING' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM USAGE_TRACKING
UNION ALL
SELECT 
    'SUBSCRIPTIONS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM SUBSCRIPTIONS
UNION ALL
SELECT 
    'INVOICES' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM INVOICES
UNION ALL
SELECT 
    'AUDIT_LOGS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM AUDIT_LOGS;
