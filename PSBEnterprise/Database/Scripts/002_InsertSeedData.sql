-- PSB Enterprise Licensing System - Seed Data
-- Version: 1.0
-- Created: 2024-07-11

-- Insert demo customers
INSERT INTO CUSTOMERS (ID, COMPANY_NAME, CONTACT_EMAIL, BILLING_EMAIL, PHONE, ADDRESS, S<PERSON><PERSON><PERSON>IP<PERSON>ON_STATUS, TRIAL_ENDS_AT)
VALUES (
    HEXTORAW('12345678123412341234123456789012'),
    'Demo Corporation',
    '<EMAIL>',
    '<EMAIL>',
    '******-0123',
    '123 Business Ave, Suite 100, Business City, BC 12345',
    'active',
    NULL
);

INSERT INTO CUSTOMERS (ID, COMPANY_NAME, CONTACT_EMAIL, BILLING_EMAIL, PHONE, SUBSCRIPTION_STATUS, TRIAL_ENDS_AT)
VALUES (
    HEXTORAW('23456789234523452345234567890123'),
    'Startup Inc',
    '<EMAIL>',
    '<EMAIL>',
    '******-0456',
    'trial',
    SYSTIMESTAMP + INTERVAL '14' DAY
);

INSERT INTO CUSTOMERS (ID, COMPANY_NAME, CONTACT_EMAIL, BILLING_EMAIL, SUBSCRIPTION_STATUS)
VALUES (
    HEXTORAW('34567890345634563456345678901234'),
    'Enterprise Solutions Ltd',
    '<EMAIL>',
    '<EMAIL>',
    'active'
);

-- Insert demo licenses
INSERT INTO LICENSES (
    ID, 
    CUSTOMER_ID, 
    LICENSE_KEY, 
    LICENSE_TOKEN, 
    TIER, 
    MAX_USERS, 
    FEATURES, 
    API_CALLS_PER_DAY, 
    EXPIRES_AT, 
    IS_ACTIVE
) VALUES (
    HEXTORAW('45678901456745674567456789012345'),
    HEXTORAW('12345678123412341234123456789012'),
    'PSB-ENT-20251231-A1B2C3D4',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0b21lcklkIjoiMTIzNDU2NzgtMTIzNC0xMjM0LTEyMzQtMTIzNDU2Nzg5MDEyIiwidGllciI6ImVudGVycHJpc2UiLCJleHBpcnlEYXRlIjoiMjAyNS0xMi0zMVQyMzo1OTo1OS45OTlaIiwibWF4VXNlcnMiOi0xLCJhcGlDYWxsc1BlckRheSI6LTEsImdlbmVyYXRlZEF0IjoiMjAyNC0wNy0xMVQwNjoyNzoxOC41MTdaIn0.demo-signature',
    'enterprise',
    -1,
    '["all-features", "sso-integration", "audit-logs", "advanced-security", "dedicated-support", "unlimited-api", "team-management"]',
    -1,
    TO_TIMESTAMP('2025-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'),
    1
);

INSERT INTO LICENSES (
    ID, 
    CUSTOMER_ID, 
    LICENSE_KEY, 
    LICENSE_TOKEN, 
    TIER, 
    MAX_USERS, 
    FEATURES, 
    API_CALLS_PER_DAY, 
    EXPIRES_AT, 
    IS_ACTIVE
) VALUES (
    HEXTORAW('56789012567856785678567890123456'),
    HEXTORAW('23456789234523452345234567890123'),
    'PSB-STA-20241225-E5F6G7H8',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0b21lcklkIjoiMjM0NTY3ODktMjM0NS0yMzQ1LTIzNDUtMjM0NTY3ODkwMTIzIiwidGllciI6InN0YXJ0ZXIiLCJleHBpcnlEYXRlIjoiMjAyNC0xMi0yNVQyMzo1OTo1OS45OTlaIiwibWF4VXNlcnMiOjUsImFwaUNhbGxzUGVyRGF5IjoxMDAsImdlbmVyYXRlZEF0IjoiMjAyNC0wNy0xMVQwNjoyNzoxOC41MTdaIn0.demo-signature',
    'starter',
    5,
    '["user-management", "basic-reporting", "email-support"]',
    100,
    TO_TIMESTAMP('2024-12-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS'),
    1
);

INSERT INTO LICENSES (
    ID, 
    CUSTOMER_ID, 
    LICENSE_KEY, 
    LICENSE_TOKEN, 
    TIER, 
    MAX_USERS, 
    FEATURES, 
    API_CALLS_PER_DAY, 
    EXPIRES_AT, 
    IS_ACTIVE
) VALUES (
    HEXTORAW('67890123678967896789678901234567'),
    HEXTORAW('34567890345634563456345678901234'),
    'PSB-PRO-20251130-I9J0K1L2',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0b21lcklkIjoiMzQ1Njc4OTAtMzQ1Ni0zNDU2LTM0NTYtMzQ1Njc4OTAxMjM0IiwidGllciI6InByb2Zlc3Npb25hbCIsImV4cGlyeURhdGUiOiIyMDI1LTExLTMwVDIzOjU5OjU5Ljk5OVoiLCJtYXhVc2VycyI6NTAsImFwaUNhbGxzUGVyRGF5IjoxMDAwLCJnZW5lcmF0ZWRBdCI6IjIwMjQtMDctMTFUMDY6Mjc6MTguNTE3WiJ9.demo-signature',
    'professional',
    50,
    '["user-management", "basic-reporting", "email-support", "advanced-analytics", "api-access", "custom-branding", "priority-support"]',
    1000,
    TO_TIMESTAMP('2025-11-30 23:59:59', 'YYYY-MM-DD HH24:MI:SS'),
    1
);

-- Insert demo users
INSERT INTO USERS (ID, CUSTOMER_ID, EMAIL, FIRST_NAME, LAST_NAME, ROLE, STATUS)
VALUES (
    HEXTORAW('78901234789078907890789012345678'),
    HEXTORAW('12345678123412341234123456789012'),
    '<EMAIL>',
    'John',
    'Administrator',
    'admin',
    'active'
);

INSERT INTO USERS (ID, CUSTOMER_ID, EMAIL, FIRST_NAME, LAST_NAME, ROLE, STATUS)
VALUES (
    HEXTORAW('89012345890189018901890123456789'),
    HEXTORAW('12345678123412341234123456789012'),
    '<EMAIL>',
    'Jane',
    'Smith',
    'user',
    'active'
);

INSERT INTO USERS (ID, CUSTOMER_ID, EMAIL, FIRST_NAME, LAST_NAME, ROLE, STATUS)
VALUES (
    HEXTORAW('90123456901290129012901234567890'),
    HEXTORAW('23456789234523452345234567890123'),
    '<EMAIL>',
    'Mike',
    'Founder',
    'admin',
    'active'
);

INSERT INTO USERS (ID, CUSTOMER_ID, EMAIL, FIRST_NAME, LAST_NAME, ROLE, STATUS)
VALUES (
    HEXTORAW('01234567012301230123012345678901'),
    HEXTORAW('34567890345634563456345678901234'),
    '<EMAIL>',
    'Sarah',
    'Johnson',
    'admin',
    'active'
);

-- Insert demo subscriptions
INSERT INTO SUBSCRIPTIONS (
    ID,
    CUSTOMER_ID,
    STRIPE_SUBSCRIPTION_ID,
    TIER,
    STATUS,
    CURRENT_PERIOD_START,
    CURRENT_PERIOD_END,
    CANCEL_AT_PERIOD_END
) VALUES (
    HEXTORAW('********************************'),
    HEXTORAW('12345678123412341234123456789012'),
    'sub_demo_enterprise_123',
    'enterprise',
    'active',
    SYSTIMESTAMP - INTERVAL '15' DAY,
    SYSTIMESTAMP + INTERVAL '15' DAY,
    0
);

INSERT INTO SUBSCRIPTIONS (
    ID,
    CUSTOMER_ID,
    TIER,
    STATUS,
    CURRENT_PERIOD_START,
    CURRENT_PERIOD_END,
    CANCEL_AT_PERIOD_END
) VALUES (
    HEXTORAW('22222222222222222222222222222222'),
    HEXTORAW('23456789234523452345234567890123'),
    'starter',
    'active',
    SYSTIMESTAMP - INTERVAL '5' DAY,
    SYSTIMESTAMP + INTERVAL '25' DAY,
    0
);

-- Insert demo usage tracking data
INSERT INTO USAGE_TRACKING (CUSTOMER_ID, USAGE_TYPE, USAGE_DATE, COUNT)
VALUES (
    HEXTORAW('12345678123412341234123456789012'),
    'api_calls',
    TRUNC(SYSDATE),
    150
);

INSERT INTO USAGE_TRACKING (CUSTOMER_ID, USAGE_TYPE, USAGE_DATE, COUNT)
VALUES (
    HEXTORAW('12345678123412341234123456789012'),
    'active_users',
    TRUNC(SYSDATE),
    2
);

INSERT INTO USAGE_TRACKING (CUSTOMER_ID, USAGE_TYPE, USAGE_DATE, COUNT)
VALUES (
    HEXTORAW('23456789234523452345234567890123'),
    'api_calls',
    TRUNC(SYSDATE),
    45
);

INSERT INTO USAGE_TRACKING (CUSTOMER_ID, USAGE_TYPE, USAGE_DATE, COUNT)
VALUES (
    HEXTORAW('23456789234523452345234567890123'),
    'active_users',
    TRUNC(SYSDATE),
    1
);

-- Insert demo audit logs
INSERT INTO AUDIT_LOGS (
    CUSTOMER_ID,
    USER_ID,
    ACTION,
    RESOURCE_TYPE,
    RESOURCE_ID,
    DETAILS,
    IP_ADDRESS,
    USER_AGENT
) VALUES (
    HEXTORAW('12345678123412341234123456789012'),
    HEXTORAW('78901234789078907890789012345678'),
    'user.login',
    'user',
    RAWTOHEX(HEXTORAW('78901234789078907890789012345678')),
    '{"login_method": "password", "success": true}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
);

INSERT INTO AUDIT_LOGS (
    CUSTOMER_ID,
    USER_ID,
    ACTION,
    RESOURCE_TYPE,
    RESOURCE_ID,
    DETAILS,
    IP_ADDRESS
) VALUES (
    HEXTORAW('12345678123412341234123456789012'),
    HEXTORAW('78901234789078907890789012345678'),
    'license.validated',
    'license',
    RAWTOHEX(HEXTORAW('45678901456745674567456789012345')),
    '{"validation_result": "success", "tier": "enterprise"}',
    '*************'
);

-- Insert demo invoices
INSERT INTO INVOICES (
    ID,
    CUSTOMER_ID,
    SUBSCRIPTION_ID,
    STRIPE_INVOICE_ID,
    AMOUNT_DUE,
    AMOUNT_PAID,
    CURRENCY,
    STATUS,
    DUE_DATE,
    PAID_AT
) VALUES (
    HEXTORAW('********************************'),
    HEXTORAW('12345678123412341234123456789012'),
    HEXTORAW('********************************'),
    'in_demo_enterprise_123',
    29900, -- $299.00 in cents
    29900,
    'USD',
    'paid',
    SYSTIMESTAMP - INTERVAL '5' DAY,
    SYSTIMESTAMP - INTERVAL '3' DAY
);

INSERT INTO INVOICES (
    ID,
    CUSTOMER_ID,
    SUBSCRIPTION_ID,
    AMOUNT_DUE,
    AMOUNT_PAID,
    CURRENCY,
    STATUS,
    DUE_DATE
) VALUES (
    HEXTORAW('44444444444444444444444444444444'),
    HEXTORAW('23456789234523452345234567890123'),
    HEXTORAW('22222222222222222222222222222222'),
    2900, -- $29.00 in cents
    0,
    'USD',
    'open',
    SYSTIMESTAMP + INTERVAL '7' DAY
);

COMMIT;

-- Display seed data summary
SELECT 'Seed data inserted successfully' as STATUS FROM DUAL;

-- Show record counts
SELECT 
    'CUSTOMERS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM CUSTOMERS
UNION ALL
SELECT 
    'LICENSES' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM LICENSES
UNION ALL
SELECT 
    'USERS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM USERS
UNION ALL
SELECT 
    'USAGE_TRACKING' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM USAGE_TRACKING
UNION ALL
SELECT 
    'SUBSCRIPTIONS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM SUBSCRIPTIONS
UNION ALL
SELECT 
    'INVOICES' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM INVOICES
UNION ALL
SELECT 
    'AUDIT_LOGS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM AUDIT_LOGS;

-- Show sample data
SELECT 'Demo customers:' as INFO FROM DUAL;
SELECT COMPANY_NAME, CONTACT_EMAIL, SUBSCRIPTION_STATUS FROM CUSTOMERS;

SELECT 'Demo licenses:' as INFO FROM DUAL;
SELECT LICENSE_KEY, TIER, MAX_USERS, API_CALLS_PER_DAY, EXPIRES_AT FROM LICENSES;
