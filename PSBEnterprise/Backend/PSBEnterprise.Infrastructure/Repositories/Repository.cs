using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using PSBEnterprise.Core.Interfaces;
using PSBEnterprise.Infrastructure.Data;

namespace PSBEnterprise.Infrastructure.Repositories;

public class Repository<T> : IRepository<T> where T : class
{
    protected readonly PSBEnterpriseDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public Repository(PSBEnterpriseDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Guid id)
    {
        return await _dbSet.FindAsync(id);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        await _dbSet.AddAsync(entity);
        return entity;
    }

    public virtual Task UpdateAsync(T entity)
    {
        _dbSet.Update(entity);
        return Task.CompletedTask;
    }

    public virtual Task DeleteAsync(T entity)
    {
        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    public virtual async Task<int> CountAsync()
    {
        return await _dbSet.CountAsync();
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.CountAsync(predicate);
    }
}

public class UnitOfWork : IUnitOfWork
{
    private readonly PSBEnterpriseDbContext _context;
    private IRepository<Core.Entities.Customer>? _customers;
    private IRepository<Core.Entities.License>? _licenses;
    private IRepository<Core.Entities.User>? _users;
    private IRepository<Core.Entities.UsageTracking>? _usageTrackings;
    private IRepository<Core.Entities.Subscription>? _subscriptions;
    private IRepository<Core.Entities.Invoice>? _invoices;
    private IRepository<Core.Entities.AuditLog>? _auditLogs;

    public UnitOfWork(PSBEnterpriseDbContext context)
    {
        _context = context;
    }

    public IRepository<Core.Entities.Customer> Customers =>
        _customers ??= new Repository<Core.Entities.Customer>(_context);

    public IRepository<Core.Entities.License> Licenses =>
        _licenses ??= new Repository<Core.Entities.License>(_context);

    public IRepository<Core.Entities.User> Users =>
        _users ??= new Repository<Core.Entities.User>(_context);

    public IRepository<Core.Entities.UsageTracking> UsageTrackings =>
        _usageTrackings ??= new Repository<Core.Entities.UsageTracking>(_context);

    public IRepository<Core.Entities.Subscription> Subscriptions =>
        _subscriptions ??= new Repository<Core.Entities.Subscription>(_context);

    public IRepository<Core.Entities.Invoice> Invoices =>
        _invoices ??= new Repository<Core.Entities.Invoice>(_context);

    public IRepository<Core.Entities.AuditLog> AuditLogs =>
        _auditLogs ??= new Repository<Core.Entities.AuditLog>(_context);

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_context.Database.CurrentTransaction != null)
        {
            await _context.Database.CurrentTransaction.CommitAsync();
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_context.Database.CurrentTransaction != null)
        {
            await _context.Database.CurrentTransaction.RollbackAsync();
        }
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
