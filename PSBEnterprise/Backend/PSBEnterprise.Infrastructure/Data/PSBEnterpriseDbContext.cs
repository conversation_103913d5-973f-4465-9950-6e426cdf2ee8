using Microsoft.EntityFrameworkCore;
using PSBEnterprise.Core.Entities;

namespace PSBEnterprise.Infrastructure.Data;

public class PSBEnterpriseDbContext : DbContext
{
    public PSBEnterpriseDbContext(DbContextOptions<PSBEnterpriseDbContext> options) : base(options)
    {
    }

    public DbSet<Customer> Customers { get; set; }
    public DbSet<License> Licenses { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UsageTracking> UsageTrackings { get; set; }
    public DbSet<Subscription> Subscriptions { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Oracle-specific settings
        ConfigureCustomer(modelBuilder);
        ConfigureLicense(modelBuilder);
        ConfigureUser(modelBuilder);
        ConfigureUsageTracking(modelBuilder);
        ConfigureSubscription(modelBuilder);
        ConfigureInvoice(modelBuilder);
        ConfigureAuditLog(modelBuilder);

        // Configure indexes
        ConfigureIndexes(modelBuilder);
    }

    private static void ConfigureCustomer(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.CompanyName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ContactEmail).IsRequired().HasMaxLength(255);
            entity.Property(e => e.BillingEmail).HasMaxLength(255);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.Address).HasColumnType("CLOB");
            entity.Property(e => e.SubscriptionStatus).HasMaxLength(20).HasDefaultValue("active");
            entity.Property(e => e.Metadata).HasColumnType("CLOB");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on email
            entity.HasIndex(e => e.ContactEmail).IsUnique();
        });
    }

    private static void ConfigureLicense(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<License>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.LicenseKey).IsRequired().HasMaxLength(255);
            entity.Property(e => e.LicenseToken).IsRequired().HasColumnType("CLOB");
            entity.Property(e => e.Tier).IsRequired().HasMaxLength(20);
            entity.Property(e => e.MaxUsers).HasDefaultValue(-1);
            entity.Property(e => e.Features).HasColumnType("CLOB");
            entity.Property(e => e.CustomFeatures).HasColumnType("CLOB");
            entity.Property(e => e.ApiCallsPerDay).HasDefaultValue(-1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on license key
            entity.HasIndex(e => e.LicenseKey).IsUnique();

            // Foreign key relationship
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Licenses)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private static void ConfigureUser(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Role).HasMaxLength(50).HasDefaultValue("user");
            entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("active");
            entity.Property(e => e.PasswordHash).HasColumnType("CLOB");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on customer_id + email
            entity.HasIndex(e => new { e.CustomerId, e.Email }).IsUnique();

            // Foreign key relationship
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Users)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private static void ConfigureUsageTracking(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UsageTracking>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.UsageType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Count).HasDefaultValue(0);
            entity.Property(e => e.Metadata).HasColumnType("CLOB");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on customer_id + usage_type + usage_date
            entity.HasIndex(e => new { e.CustomerId, e.UsageType, e.UsageDate }).IsUnique();

            // Foreign key relationship
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.UsageTrackings)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private static void ConfigureSubscription(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Subscription>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.StripeSubscriptionId).HasMaxLength(255);
            entity.Property(e => e.Tier).IsRequired().HasMaxLength(20);
            entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
            entity.Property(e => e.CancelAtPeriodEnd).HasDefaultValue(false);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on Stripe subscription ID
            entity.HasIndex(e => e.StripeSubscriptionId).IsUnique();

            // Foreign key relationship
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Subscriptions)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private static void ConfigureInvoice(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Invoice>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.StripeInvoiceId).HasMaxLength(255);
            entity.Property(e => e.AmountPaid).HasDefaultValue(0);
            entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("USD");
            entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Unique constraint on Stripe invoice ID
            entity.HasIndex(e => e.StripeInvoiceId).IsUnique();

            // Foreign key relationships
            entity.HasOne(e => e.Customer)
                  .WithMany()
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Subscription)
                  .WithMany(s => s.Invoices)
                  .HasForeignKey(e => e.SubscriptionId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private static void ConfigureAuditLog(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("SYS_GUID()");
            
            entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ResourceType).HasMaxLength(50);
            entity.Property(e => e.ResourceId).HasMaxLength(255);
            entity.Property(e => e.Details).HasColumnType("CLOB");
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasColumnType("CLOB");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("SYSTIMESTAMP");

            // Foreign key relationships
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.AuditLogs)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.User)
                  .WithMany(u => u.AuditLogs)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private static void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Performance indexes
        modelBuilder.Entity<Customer>()
            .HasIndex(e => e.ContactEmail)
            .HasDatabaseName("IDX_CUSTOMERS_EMAIL");

        modelBuilder.Entity<License>()
            .HasIndex(e => e.CustomerId)
            .HasDatabaseName("IDX_LICENSES_CUSTOMER_ID");

        modelBuilder.Entity<License>()
            .HasIndex(e => e.LicenseKey)
            .HasDatabaseName("IDX_LICENSES_KEY");

        modelBuilder.Entity<User>()
            .HasIndex(e => e.CustomerId)
            .HasDatabaseName("IDX_USERS_CUSTOMER_ID");

        modelBuilder.Entity<User>()
            .HasIndex(e => new { e.CustomerId, e.Email })
            .HasDatabaseName("IDX_USERS_CUSTOMER_EMAIL");

        modelBuilder.Entity<UsageTracking>()
            .HasIndex(e => new { e.CustomerId, e.UsageDate })
            .HasDatabaseName("IDX_USAGE_CUSTOMER_DATE");

        modelBuilder.Entity<UsageTracking>()
            .HasIndex(e => e.UsageType)
            .HasDatabaseName("IDX_USAGE_TYPE");

        modelBuilder.Entity<Subscription>()
            .HasIndex(e => e.CustomerId)
            .HasDatabaseName("IDX_SUBSCRIPTIONS_CUSTOMER_ID");

        modelBuilder.Entity<AuditLog>()
            .HasIndex(e => e.CustomerId)
            .HasDatabaseName("IDX_AUDIT_LOGS_CUSTOMER_ID");

        modelBuilder.Entity<AuditLog>()
            .HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IDX_AUDIT_LOGS_CREATED_AT");
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Modified)
            .Select(e => e.Entity);

        foreach (var entity in entries)
        {
            if (entity.GetType().GetProperty("UpdatedAt") != null)
            {
                entity.GetType().GetProperty("UpdatedAt")!.SetValue(entity, DateTime.UtcNow);
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
