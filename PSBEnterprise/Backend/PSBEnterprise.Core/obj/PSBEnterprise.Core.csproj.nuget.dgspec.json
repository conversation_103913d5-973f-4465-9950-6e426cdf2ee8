{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj", "projectName": "PSBEnterprise.Core", "projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}