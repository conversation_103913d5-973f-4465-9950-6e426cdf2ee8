namespace PSBEnterprise.Core.DTOs;

public class LicenseGenerationRequest
{
    public Guid CustomerId { get; set; }
    public string Tier { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public int? MaxUsers { get; set; }
    public int? ApiCallsPerDay { get; set; }
    public Dictionary<string, bool>? CustomFeatures { get; set; }
}

public class LicenseGenerationResult
{
    public bool Success { get; set; }
    public string LicenseKey { get; set; } = string.Empty;
    public string LicenseToken { get; set; } = string.Empty;
    public string Tier { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public string? ErrorMessage { get; set; }
}

public class LicenseValidationResult
{
    public bool IsValid { get; set; }
    public Guid CustomerId { get; set; }
    public string Tier { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public string[] Features { get; set; } = Array.Empty<string>();
    public int MaxUsers { get; set; }
    public int ApiCallsPerDay { get; set; }
    public string? ErrorMessage { get; set; }
}

public class LicenseInfoDto
{
    public CustomerInfoDto Customer { get; set; } = new();
    public LicenseDetailsDto License { get; set; } = new();
    public Dictionary<string, bool> Features { get; set; } = new();
    public LicenseLimitsDto Limits { get; set; } = new();
}

public class CustomerInfoDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string Tier { get; set; } = string.Empty;
    public string SubscriptionStatus { get; set; } = string.Empty;
}

public class LicenseDetailsDto
{
    public string Tier { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public int MaxUsers { get; set; }
    public int ApiCallsPerDay { get; set; }
    public bool IsActive { get; set; }
}

public class LicenseLimitsDto
{
    public int MaxUsers { get; set; }
    public int ApiCallsPerDay { get; set; }
}

public class FeatureConfigDto
{
    public string Tier { get; set; } = string.Empty;
    public Dictionary<string, bool> Features { get; set; } = new();
    public LicenseLimitsDto Limits { get; set; } = new();
}

public class UsageStatsDto
{
    public CurrentUsageDto Current { get; set; } = new();
    public UsageLimitsDto Limits { get; set; } = new();
    public UsageSummaryDto Summary { get; set; } = new();
    public string Period { get; set; } = string.Empty;
}

public class CurrentUsageDto
{
    public int ApiCalls { get; set; }
    public int ActiveUsers { get; set; }
}

public class UsageLimitsDto
{
    public UsageLimitDto ApiUsage { get; set; } = new();
    public UsageLimitDto UserUsage { get; set; } = new();
    public string[] Warnings { get; set; } = Array.Empty<string>();
}

public class UsageLimitDto
{
    public bool Allowed { get; set; }
    public int Limit { get; set; }
    public int Current { get; set; }
    public int Remaining { get; set; }
}

public class UsageSummaryDto
{
    public Dictionary<string, UsageMetricDto> Metrics { get; set; } = new();
}

public class UsageMetricDto
{
    public int Total { get; set; }
    public double AvgDaily { get; set; }
    public int PeakDaily { get; set; }
}

public class UsageLimitCheckResult
{
    public UsageLimitDto ApiUsage { get; set; } = new();
    public UsageLimitDto UserUsage { get; set; } = new();
    public string[] Warnings { get; set; } = Array.Empty<string>();
}

public class CreateCustomerRequest
{
    public string CompanyName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string? BillingEmail { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string SubscriptionStatus { get; set; } = "active";
    public DateTime? TrialEndsAt { get; set; }
}

public class UpdateCustomerRequest
{
    public string? CompanyName { get; set; }
    public string? ContactEmail { get; set; }
    public string? BillingEmail { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? SubscriptionStatus { get; set; }
    public DateTime? TrialEndsAt { get; set; }
}

public class CustomerStatsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int ApiCallsToday { get; set; }
    public string CurrentTier { get; set; } = string.Empty;
    public DateTime? LicenseExpiryDate { get; set; }
    public Dictionary<string, int> FeatureUsage { get; set; } = new();
}

public class CreateUserRequest
{
    public Guid CustomerId { get; set; }
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string Role { get; set; } = "user";
}

public class UpdateUserRequest
{
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Role { get; set; }
    public string? Status { get; set; }
}

public class AuditLogRequest
{
    public Guid CustomerId { get; set; }
    public Guid? UserId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? ResourceType { get; set; }
    public string? ResourceId { get; set; }
    public object? Details { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}
