using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PSBEnterprise.Core.Entities;

[Table("USERS")]
public class User
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [Required]
    [MaxLength(255)]
    [Column("EMAIL")]
    public string Email { get; set; } = string.Empty;

    [MaxLength(100)]
    [Column("FIRST_NAME")]
    public string? FirstName { get; set; }

    [MaxLength(100)]
    [Column("LAST_NAME")]
    public string? LastName { get; set; }

    [MaxLength(50)]
    [Column("ROLE")]
    public string Role { get; set; } = UserRoles.User;

    [MaxLength(20)]
    [Column("STATUS")]
    public string Status { get; set; } = UserStatuses.Active;

    [Column("LAST_LOGIN_AT")]
    public DateTime? LastLoginAt { get; set; }

    [Column("PASSWORD_HASH")]
    public string? PasswordHash { get; set; }

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UPDATED_AT")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
}

public static class UserRoles
{
    public const string User = "user";
    public const string Admin = "admin";
    public const string Manager = "manager";
    public const string SuperAdmin = "super_admin";
}

public static class UserStatuses
{
    public const string Active = "active";
    public const string Inactive = "inactive";
    public const string Suspended = "suspended";
    public const string Pending = "pending";
}
