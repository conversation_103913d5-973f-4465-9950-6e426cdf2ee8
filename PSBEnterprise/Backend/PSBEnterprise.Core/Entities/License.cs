using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PSBEnterprise.Core.Entities;

[Table("LICENSES")]
public class License
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [Required]
    [MaxLength(255)]
    [Column("LICENSE_KEY")]
    public string LicenseKey { get; set; } = string.Empty;

    [Required]
    [Column("LICENSE_TOKEN")]
    public string LicenseToken { get; set; } = string.Empty;

    [Required]
    [MaxLength(20)]
    [Column("TIER")]
    public string Tier { get; set; } = string.Empty;

    [Column("MAX_USERS")]
    public int MaxUsers { get; set; } = -1; // -1 means unlimited

    [Column("FEATURES")]
    public string? Features { get; set; } // JSON string

    [Column("CUSTOM_FEATURES")]
    public string? CustomFeatures { get; set; } // JSON string

    [Column("API_CALLS_PER_DAY")]
    public int ApiCallsPerDay { get; set; } = -1; // -1 means unlimited

    [Required]
    [Column("EXPIRES_AT")]
    public DateTime ExpiresAt { get; set; }

    [Column("IS_ACTIVE")]
    public bool IsActive { get; set; } = true;

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UPDATED_AT")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;
}

public static class LicenseTiers
{
    public const string Starter = "starter";
    public const string Professional = "professional";
    public const string Enterprise = "enterprise";
    public const string Custom = "custom";

    public static readonly Dictionary<string, LicenseTierInfo> TierInfo = new()
    {
        {
            Starter,
            new LicenseTierInfo
            {
                Name = "Starter",
                MaxUsers = 5,
                ApiCallsPerDay = 100,
                Price = 29,
                Features = new[] { "user-management", "basic-reporting", "email-support" }
            }
        },
        {
            Professional,
            new LicenseTierInfo
            {
                Name = "Professional",
                MaxUsers = 50,
                ApiCallsPerDay = 1000,
                Price = 99,
                Features = new[] { "user-management", "basic-reporting", "email-support", "advanced-analytics", "api-access", "custom-branding", "priority-support" }
            }
        },
        {
            Enterprise,
            new LicenseTierInfo
            {
                Name = "Enterprise",
                MaxUsers = -1,
                ApiCallsPerDay = -1,
                Price = 299,
                Features = new[] { "all-features", "sso-integration", "audit-logs", "advanced-security", "dedicated-support", "unlimited-api", "team-management" }
            }
        },
        {
            Custom,
            new LicenseTierInfo
            {
                Name = "Custom",
                MaxUsers = -1,
                ApiCallsPerDay = -1,
                Price = 0, // Contact sales
                Features = new[] { "all-features", "white-label", "custom-integrations", "sla-guarantees", "custom-development" }
            }
        }
    };
}

public class LicenseTierInfo
{
    public string Name { get; set; } = string.Empty;
    public int MaxUsers { get; set; }
    public int ApiCallsPerDay { get; set; }
    public decimal Price { get; set; }
    public string[] Features { get; set; } = Array.Empty<string>();
}
