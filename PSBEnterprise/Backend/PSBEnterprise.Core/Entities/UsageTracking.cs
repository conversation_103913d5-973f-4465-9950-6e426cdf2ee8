using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PSBEnterprise.Core.Entities;

[Table("USAGE_TRACKING")]
public class UsageTracking
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [Required]
    [MaxLength(50)]
    [Column("USAGE_TYPE")]
    public string UsageType { get; set; } = string.Empty;

    [Required]
    [Column("USAGE_DATE")]
    public DateTime UsageDate { get; set; }

    [Column("COUNT")]
    public int Count { get; set; } = 0;

    [Column("METADATA")]
    public string? Metadata { get; set; } // JSON string

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UPDATED_AT")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;
}

public static class UsageTypes
{
    public const string ApiCalls = "api_calls";
    public const string ActiveUsers = "active_users";
    public const string FeatureUsage = "feature_usage";
    public const string DataStorage = "data_storage";
    public const string Bandwidth = "bandwidth";
}

[Table("SUBSCRIPTIONS")]
public class Subscription
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [MaxLength(255)]
    [Column("STRIPE_SUBSCRIPTION_ID")]
    public string? StripeSubscriptionId { get; set; }

    [Required]
    [MaxLength(20)]
    [Column("TIER")]
    public string Tier { get; set; } = string.Empty;

    [Required]
    [MaxLength(20)]
    [Column("STATUS")]
    public string Status { get; set; } = string.Empty;

    [Required]
    [Column("CURRENT_PERIOD_START")]
    public DateTime CurrentPeriodStart { get; set; }

    [Required]
    [Column("CURRENT_PERIOD_END")]
    public DateTime CurrentPeriodEnd { get; set; }

    [Column("CANCEL_AT_PERIOD_END")]
    public bool CancelAtPeriodEnd { get; set; } = false;

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UPDATED_AT")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}

[Table("INVOICES")]
public class Invoice
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [Column("SUBSCRIPTION_ID")]
    public Guid? SubscriptionId { get; set; }

    [MaxLength(255)]
    [Column("STRIPE_INVOICE_ID")]
    public string? StripeInvoiceId { get; set; }

    [Required]
    [Column("AMOUNT_DUE")]
    public int AmountDue { get; set; } // in cents

    [Column("AMOUNT_PAID")]
    public int AmountPaid { get; set; } = 0;

    [MaxLength(3)]
    [Column("CURRENCY")]
    public string Currency { get; set; } = "USD";

    [Required]
    [MaxLength(20)]
    [Column("STATUS")]
    public string Status { get; set; } = string.Empty;

    [Column("DUE_DATE")]
    public DateTime? DueDate { get; set; }

    [Column("PAID_AT")]
    public DateTime? PaidAt { get; set; }

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    [ForeignKey("SubscriptionId")]
    public virtual Subscription? Subscription { get; set; }
}

[Table("AUDIT_LOGS")]
public class AuditLog
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("CUSTOMER_ID")]
    public Guid CustomerId { get; set; }

    [Column("USER_ID")]
    public Guid? UserId { get; set; }

    [Required]
    [MaxLength(100)]
    [Column("ACTION")]
    public string Action { get; set; } = string.Empty;

    [MaxLength(50)]
    [Column("RESOURCE_TYPE")]
    public string? ResourceType { get; set; }

    [MaxLength(255)]
    [Column("RESOURCE_ID")]
    public string? ResourceId { get; set; }

    [Column("DETAILS")]
    public string? Details { get; set; } // JSON string

    [MaxLength(45)]
    [Column("IP_ADDRESS")]
    public string? IpAddress { get; set; }

    [Column("USER_AGENT")]
    public string? UserAgent { get; set; }

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    [ForeignKey("UserId")]
    public virtual User? User { get; set; }
}
