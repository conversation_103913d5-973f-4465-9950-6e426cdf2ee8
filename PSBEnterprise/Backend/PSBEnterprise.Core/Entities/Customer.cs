using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PSBEnterprise.Core.Entities;

[Table("CUSTOMERS")]
public class Customer
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [MaxLength(255)]
    [Column("COMPANY_NAME")]
    public string CompanyName { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    [Column("CONTACT_EMAIL")]
    public string ContactEmail { get; set; } = string.Empty;

    [MaxLength(255)]
    [Column("BILLING_EMAIL")]
    public string? BillingEmail { get; set; }

    [MaxLength(50)]
    [Column("PHONE")]
    public string? Phone { get; set; }

    [Column("ADDRESS")]
    public string? Address { get; set; }

    [Required]
    [MaxLength(20)]
    [Column("SUBSCRIPTION_STATUS")]
    public string SubscriptionStatus { get; set; } = "active";

    [Column("TRIAL_ENDS_AT")]
    public DateTime? TrialEndsAt { get; set; }

    [Column("METADATA")]
    public string? Metadata { get; set; }

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UPDATED_AT")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<License> Licenses { get; set; } = new List<License>();
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<UsageTracking> UsageTrackings { get; set; } = new List<UsageTracking>();
    public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();
    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
}

public static class SubscriptionStatuses
{
    public const string Active = "active";
    public const string Suspended = "suspended";
    public const string Cancelled = "cancelled";
    public const string Trial = "trial";
}
