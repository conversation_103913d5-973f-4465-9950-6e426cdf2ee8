using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using PSBEnterprise.Core.DTOs;
using PSBEnterprise.Core.Entities;
using PSBEnterprise.Core.Interfaces;

namespace PSBEnterprise.Core.Services;

public class LicenseService : ILicenseService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IFeatureFlagService _featureFlagService;
    private readonly IUsageTrackingService _usageTrackingService;
    private readonly IConfiguration _configuration;
    private readonly string _secretKey;

    public LicenseService(
        IUnitOfWork unitOfWork,
        IFeatureFlagService featureFlagService,
        IUsageTrackingService usageTrackingService,
        IConfiguration configuration)
    {
        _unitOfWork = unitOfWork;
        _featureFlagService = featureFlagService;
        _usageTrackingService = usageTrackingService;
        _configuration = configuration;
        _secretKey = _configuration["LicenseSettings:SecretKey"] ?? throw new InvalidOperationException("License secret key not configured");
    }

    public async Task<LicenseGenerationResult> GenerateLicenseAsync(LicenseGenerationRequest request)
    {
        try
        {
            // Validate customer exists
            var customer = await _unitOfWork.Customers.GetByIdAsync(request.CustomerId);
            if (customer == null)
            {
                return new LicenseGenerationResult
                {
                    Success = false,
                    ErrorMessage = "Customer not found"
                };
            }

            // Get tier information
            if (!LicenseTiers.TierInfo.TryGetValue(request.Tier, out var tierInfo))
            {
                return new LicenseGenerationResult
                {
                    Success = false,
                    ErrorMessage = "Invalid license tier"
                };
            }

            // Generate license key and token
            var licenseKey = GenerateLicenseKey(request.CustomerId, request.Tier, request.ExpiryDate);
            var licenseToken = GenerateLicenseToken(request);

            // Create license entity
            var license = new License
            {
                CustomerId = request.CustomerId,
                LicenseKey = licenseKey,
                LicenseToken = licenseToken,
                Tier = request.Tier,
                MaxUsers = request.MaxUsers ?? tierInfo.MaxUsers,
                ApiCallsPerDay = request.ApiCallsPerDay ?? tierInfo.ApiCallsPerDay,
                Features = JsonSerializer.Serialize(tierInfo.Features),
                CustomFeatures = request.CustomFeatures != null ? JsonSerializer.Serialize(request.CustomFeatures) : null,
                ExpiresAt = request.ExpiryDate,
                IsActive = true
            };

            // Deactivate existing licenses for this customer
            var existingLicenses = await _unitOfWork.Licenses.FindAsync(l => l.CustomerId == request.CustomerId && l.IsActive);
            foreach (var existingLicense in existingLicenses)
            {
                existingLicense.IsActive = false;
                await _unitOfWork.Licenses.UpdateAsync(existingLicense);
            }

            // Save new license
            await _unitOfWork.Licenses.AddAsync(license);
            await _unitOfWork.SaveChangesAsync();

            return new LicenseGenerationResult
            {
                Success = true,
                LicenseKey = licenseKey,
                LicenseToken = licenseToken,
                Tier = request.Tier,
                ExpiryDate = request.ExpiryDate
            };
        }
        catch (Exception ex)
        {
            return new LicenseGenerationResult
            {
                Success = false,
                ErrorMessage = $"Failed to generate license: {ex.Message}"
            };
        }
    }

    public async Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey, string licenseToken)
    {
        try
        {
            // Validate JWT token
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(licenseToken, validationParameters, out var validatedToken);
            var jwtToken = (JwtSecurityToken)validatedToken;

            // Extract claims
            var customerIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == "customerId")?.Value;
            var tierClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == "tier")?.Value;
            var expiryDateClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == "expiryDate")?.Value;

            if (string.IsNullOrEmpty(customerIdClaim) || string.IsNullOrEmpty(tierClaim) || string.IsNullOrEmpty(expiryDateClaim))
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Invalid token claims"
                };
            }

            var customerId = Guid.Parse(customerIdClaim);
            var expiryDate = DateTime.Parse(expiryDateClaim);

            // Check if license has expired
            if (DateTime.UtcNow > expiryDate)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "License has expired"
                };
            }

            // Verify license key matches
            var expectedLicenseKey = GenerateLicenseKey(customerId, tierClaim, expiryDate);
            if (licenseKey != expectedLicenseKey)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Invalid license key"
                };
            }

            // Get license from database
            var license = await GetActiveLicenseAsync(customerId);
            if (license == null || !license.IsActive)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "License not found or inactive"
                };
            }

            // Get features
            var features = JsonSerializer.Deserialize<string[]>(license.Features ?? "[]") ?? Array.Empty<string>();

            return new LicenseValidationResult
            {
                IsValid = true,
                CustomerId = customerId,
                Tier = tierClaim,
                ExpiryDate = expiryDate,
                Features = features,
                MaxUsers = license.MaxUsers,
                ApiCallsPerDay = license.ApiCallsPerDay
            };
        }
        catch (Exception ex)
        {
            return new LicenseValidationResult
            {
                IsValid = false,
                ErrorMessage = $"License validation failed: {ex.Message}"
            };
        }
    }

    public async Task<LicenseInfoDto> GetLicenseInfoAsync(Guid customerId)
    {
        var customer = await _unitOfWork.Customers.GetByIdAsync(customerId);
        var license = await GetActiveLicenseAsync(customerId);

        if (customer == null || license == null)
        {
            throw new InvalidOperationException("Customer or license not found");
        }

        var features = JsonSerializer.Deserialize<string[]>(license.Features ?? "[]") ?? Array.Empty<string>();
        var featureDict = features.ToDictionary(f => f, f => true);

        return new LicenseInfoDto
        {
            Customer = new CustomerInfoDto
            {
                Id = customer.Id,
                CompanyName = customer.CompanyName,
                Tier = license.Tier,
                SubscriptionStatus = customer.SubscriptionStatus
            },
            License = new LicenseDetailsDto
            {
                Tier = license.Tier,
                ExpiryDate = license.ExpiresAt,
                MaxUsers = license.MaxUsers,
                ApiCallsPerDay = license.ApiCallsPerDay,
                IsActive = license.IsActive
            },
            Features = featureDict,
            Limits = new LicenseLimitsDto
            {
                MaxUsers = license.MaxUsers,
                ApiCallsPerDay = license.ApiCallsPerDay
            }
        };
    }

    public async Task<bool> IsFeatureEnabledAsync(Guid customerId, string feature)
    {
        var license = await GetActiveLicenseAsync(customerId);
        if (license == null) return false;

        // Check tier-based features
        if (_featureFlagService.IsFeatureEnabled(feature, license.Tier))
        {
            return true;
        }

        // Check custom features
        if (!string.IsNullOrEmpty(license.CustomFeatures))
        {
            var customFeatures = JsonSerializer.Deserialize<Dictionary<string, bool>>(license.CustomFeatures);
            return customFeatures?.GetValueOrDefault(feature, false) ?? false;
        }

        return false;
    }

    public async Task<UsageStatsDto> GetUsageStatsAsync(Guid customerId, int days = 30)
    {
        return await _usageTrackingService.GetUsageStatsAsync(customerId, DateTime.UtcNow.AddDays(-days), DateTime.UtcNow);
    }

    public async Task<bool> CheckUserLimitAsync(Guid customerId, int currentUserCount)
    {
        var license = await GetActiveLicenseAsync(customerId);
        if (license == null) return false;

        if (license.MaxUsers == -1) return true; // Unlimited
        return currentUserCount <= license.MaxUsers;
    }

    public async Task<bool> CheckApiLimitAsync(Guid customerId, int currentApiCalls)
    {
        var license = await GetActiveLicenseAsync(customerId);
        if (license == null) return false;

        if (license.ApiCallsPerDay == -1) return true; // Unlimited
        return currentApiCalls <= license.ApiCallsPerDay;
    }

    public async Task<License?> GetActiveLicenseAsync(Guid customerId)
    {
        var licenses = await _unitOfWork.Licenses.FindAsync(l => l.CustomerId == customerId && l.IsActive);
        return licenses.FirstOrDefault();
    }

    public async Task<bool> RenewLicenseAsync(Guid licenseId, DateTime newExpiryDate)
    {
        var license = await _unitOfWork.Licenses.GetByIdAsync(licenseId);
        if (license == null) return false;

        license.ExpiresAt = newExpiryDate;
        license.UpdatedAt = DateTime.UtcNow;

        await _unitOfWork.Licenses.UpdateAsync(license);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> UpgradeLicenseAsync(Guid customerId, string newTier)
    {
        if (!LicenseTiers.TierInfo.TryGetValue(newTier, out var tierInfo))
        {
            return false;
        }

        var license = await GetActiveLicenseAsync(customerId);
        if (license == null) return false;

        license.Tier = newTier;
        license.MaxUsers = tierInfo.MaxUsers;
        license.ApiCallsPerDay = tierInfo.ApiCallsPerDay;
        license.Features = JsonSerializer.Serialize(tierInfo.Features);
        license.UpdatedAt = DateTime.UtcNow;

        await _unitOfWork.Licenses.UpdateAsync(license);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    private string GenerateLicenseKey(Guid customerId, string tier, DateTime expiryDate)
    {
        var tierCode = tier.Substring(0, Math.Min(3, tier.Length)).ToUpper();
        var expiryCode = expiryDate.ToString("yyyyMMdd");
        var data = $"{customerId}:{tier}:{expiryDate.Ticks}";
        
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_secretKey));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        var hashString = Convert.ToHexString(hash)[..8];

        return $"PSB-{tierCode}-{expiryCode}-{hashString}";
    }

    private string GenerateLicenseToken(LicenseGenerationRequest request)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_secretKey);

        var claims = new List<Claim>
        {
            new("customerId", request.CustomerId.ToString()),
            new("tier", request.Tier),
            new("expiryDate", request.ExpiryDate.ToString("O")),
            new("maxUsers", (request.MaxUsers ?? -1).ToString()),
            new("apiCallsPerDay", (request.ApiCallsPerDay ?? -1).ToString()),
            new("generatedAt", DateTime.UtcNow.ToString("O"))
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = request.ExpiryDate,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}
