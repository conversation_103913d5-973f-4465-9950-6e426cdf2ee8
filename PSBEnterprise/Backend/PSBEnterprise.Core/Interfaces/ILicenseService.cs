using PSBEnterprise.Core.DTOs;
using PSBEnterprise.Core.Entities;

namespace PSBEnterprise.Core.Interfaces;

public interface ILicenseService
{
    Task<LicenseGenerationResult> GenerateLicenseAsync(LicenseGenerationRequest request);
    Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey, string licenseToken);
    Task<LicenseInfoDto> GetLicenseInfoAsync(Guid customerId);
    Task<bool> IsFeatureEnabledAsync(Guid customerId, string feature);
    Task<UsageStatsDto> GetUsageStatsAsync(Guid customerId, int days = 30);
    Task<bool> CheckUserLimitAsync(Guid customerId, int currentUserCount);
    Task<bool> CheckApiLimitAsync(Guid customerId, int currentApiCalls);
    Task<License?> GetActiveLicenseAsync(Guid customerId);
    Task<bool> RenewLicenseAsync(Guid licenseId, DateTime newExpiryDate);
    Task<bool> UpgradeLicenseAsync(Guid customerId, string newTier);
}

public interface IFeatureFlagService
{
    bool IsFeatureEnabled(string feature, string tier);
    string[] GetEnabledFeatures(string tier);
    FeatureConfigDto GetFeatureConfig(string tier);
    bool CheckUserLimit(string tier, int currentUsers);
    bool CheckApiLimit(string tier, int currentApiCalls);
    void AddCustomFeature(Guid customerId, string feature, bool enabled = true);
    bool IsCustomFeatureEnabled(Guid customerId, string feature);
    string[] GetCustomerFeatures(Guid customerId, string tier);
}

public interface IUsageTrackingService
{
    Task TrackApiUsageAsync(Guid customerId, string endpoint, string method = "GET");
    Task TrackUserCountAsync(Guid customerId, int activeUsers);
    Task TrackFeatureUsageAsync(Guid customerId, string feature, object? metadata = null);
    Task<int> GetCurrentApiUsageAsync(Guid customerId);
    Task<int> GetCurrentUserCountAsync(Guid customerId);
    Task<UsageStatsDto> GetUsageStatsAsync(Guid customerId, DateTime startDate, DateTime endDate);
    Task<UsageSummaryDto> GetUsageSummaryAsync(Guid customerId, int days = 30);
    Task<UsageLimitCheckResult> CheckUsageLimitsAsync(Guid customerId, string tier);
}

public interface ICustomerService
{
    Task<Customer> CreateCustomerAsync(CreateCustomerRequest request);
    Task<Customer?> GetCustomerAsync(Guid customerId);
    Task<Customer?> GetCustomerByEmailAsync(string email);
    Task<IEnumerable<Customer>> GetCustomersAsync(int page = 1, int pageSize = 50);
    Task<bool> UpdateCustomerAsync(Guid customerId, UpdateCustomerRequest request);
    Task<bool> DeleteCustomerAsync(Guid customerId);
    Task<CustomerStatsDto> GetCustomerStatsAsync(Guid customerId);
}

public interface IUserService
{
    Task<User> CreateUserAsync(CreateUserRequest request);
    Task<User?> GetUserAsync(Guid userId);
    Task<User?> GetUserByEmailAsync(string email, Guid customerId);
    Task<IEnumerable<User>> GetUsersByCustomerAsync(Guid customerId);
    Task<bool> UpdateUserAsync(Guid userId, UpdateUserRequest request);
    Task<bool> DeleteUserAsync(Guid userId);
    Task<bool> ActivateUserAsync(Guid userId);
    Task<bool> DeactivateUserAsync(Guid userId);
    Task<int> GetActiveUserCountAsync(Guid customerId);
}

public interface IAuditLogService
{
    Task LogAsync(AuditLogRequest request);
    Task<IEnumerable<AuditLog>> GetAuditLogsAsync(Guid customerId, int page = 1, int pageSize = 50);
    Task<IEnumerable<AuditLog>> GetAuditLogsByUserAsync(Guid userId, int page = 1, int pageSize = 50);
    Task<IEnumerable<AuditLog>> GetAuditLogsByActionAsync(string action, int page = 1, int pageSize = 50);
}

public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(Guid id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(System.Linq.Expressions.Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task<int> CountAsync();
    Task<int> CountAsync(System.Linq.Expressions.Expression<Func<T, bool>> predicate);
}

public interface IUnitOfWork : IDisposable
{
    IRepository<Customer> Customers { get; }
    IRepository<License> Licenses { get; }
    IRepository<User> Users { get; }
    IRepository<UsageTracking> UsageTrackings { get; }
    IRepository<Subscription> Subscriptions { get; }
    IRepository<Invoice> Invoices { get; }
    IRepository<AuditLog> AuditLogs { get; }
    
    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}
