{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Tests/PSBEnterprise.Tests.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/PSBEnterprise.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/PSBEnterprise.API.csproj", "projectName": "PSBEnterprise.API", "projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/PSBEnterprise.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj"}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.RateLimiting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj", "projectName": "PSBEnterprise.Core", "projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj", "projectName": "PSBEnterprise.Infrastructure", "projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[8.21.121, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Tests/PSBEnterprise.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Tests/PSBEnterprise.Tests.csproj", "projectName": "PSBEnterprise.Tests", "projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Tests/PSBEnterprise.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/PSBEnterprise.API.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.API/PSBEnterprise.API.csproj"}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Core/PSBEnterprise.Core.csproj"}, "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Enterpize licences/PSBEnterprise/Backend/PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.6.1, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}