using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PSBEnterprise.Core.DTOs;
using PSBEnterprise.Core.Interfaces;

namespace PSBEnterprise.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminController : ControllerBase
{
    private readonly ILicenseService _licenseService;
    private readonly ICustomerService _customerService;
    private readonly IUserService _userService;
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<AdminController> _logger;

    public AdminController(
        ILicenseService licenseService,
        ICustomerService customerService,
        IUserService userService,
        IAuditLogService auditLogService,
        ILogger<AdminController> logger)
    {
        _licenseService = licenseService;
        _customerService = customerService;
        _userService = userService;
        _auditLogService = auditLogService;
        _logger = logger;
    }

    /// <summary>
    /// Generate a new license for a customer
    /// </summary>
    [HttpPost("license/generate")]
    public async Task<ActionResult<LicenseGenerationResult>> GenerateLicense([FromBody] LicenseGenerationRequest request)
    {
        try
        {
            var result = await _licenseService.GenerateLicenseAsync(request);
            
            if (!result.Success)
            {
                return BadRequest(new { error = result.ErrorMessage });
            }

            // Log the license generation
            await _auditLogService.LogAsync(new AuditLogRequest
            {
                CustomerId = request.CustomerId,
                Action = "license.generated",
                ResourceType = "license",
                Details = new { tier = request.Tier, expiryDate = request.ExpiryDate },
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                UserAgent = Request.Headers.UserAgent.ToString()
            });

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating license for customer {CustomerId}", request.CustomerId);
            return StatusCode(500, new { error = "Failed to generate license" });
        }
    }

    /// <summary>
    /// Get all customers with pagination
    /// </summary>
    [HttpGet("customers")]
    public async Task<ActionResult<CustomerListResponse>> GetCustomers([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            var customers = await _customerService.GetCustomersAsync(page, pageSize);
            
            return Ok(new CustomerListResponse
            {
                Customers = customers,
                Page = page,
                PageSize = pageSize,
                TotalCount = customers.Count()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customers list");
            return StatusCode(500, new { error = "Failed to get customers" });
        }
    }

    /// <summary>
    /// Create a new customer
    /// </summary>
    [HttpPost("customers")]
    public async Task<ActionResult<Core.Entities.Customer>> CreateCustomer([FromBody] CreateCustomerRequest request)
    {
        try
        {
            var customer = await _customerService.CreateCustomerAsync(request);
            
            // Log customer creation
            await _auditLogService.LogAsync(new AuditLogRequest
            {
                CustomerId = customer.Id,
                Action = "customer.created",
                ResourceType = "customer",
                ResourceId = customer.Id.ToString(),
                Details = new { companyName = customer.CompanyName, contactEmail = customer.ContactEmail },
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                UserAgent = Request.Headers.UserAgent.ToString()
            });

            return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer");
            return StatusCode(500, new { error = "Failed to create customer" });
        }
    }

    /// <summary>
    /// Get customer by ID
    /// </summary>
    [HttpGet("customers/{id}")]
    public async Task<ActionResult<Core.Entities.Customer>> GetCustomer(Guid id)
    {
        try
        {
            var customer = await _customerService.GetCustomerAsync(id);
            
            if (customer == null)
            {
                return NotFound(new { error = "Customer not found" });
            }

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer {CustomerId}", id);
            return StatusCode(500, new { error = "Failed to get customer" });
        }
    }

    /// <summary>
    /// Get customer statistics
    /// </summary>
    [HttpGet("customers/{id}/stats")]
    public async Task<ActionResult<CustomerStatsDto>> GetCustomerStats(Guid id)
    {
        try
        {
            var stats = await _customerService.GetCustomerStatsAsync(id);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer stats for {CustomerId}", id);
            return StatusCode(500, new { error = "Failed to get customer statistics" });
        }
    }

    /// <summary>
    /// Upgrade customer license tier
    /// </summary>
    [HttpPost("customers/{id}/upgrade")]
    public async Task<ActionResult> UpgradeCustomer(Guid id, [FromBody] UpgradeRequest request)
    {
        try
        {
            var success = await _licenseService.UpgradeLicenseAsync(id, request.NewTier);
            
            if (!success)
            {
                return BadRequest(new { error = "Failed to upgrade license" });
            }

            // Log the upgrade
            await _auditLogService.LogAsync(new AuditLogRequest
            {
                CustomerId = id,
                Action = "license.upgraded",
                ResourceType = "license",
                Details = new { newTier = request.NewTier },
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                UserAgent = Request.Headers.UserAgent.ToString()
            });

            return Ok(new { message = "License upgraded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upgrading customer {CustomerId} to tier {NewTier}", id, request.NewTier);
            return StatusCode(500, new { error = "Failed to upgrade license" });
        }
    }

    /// <summary>
    /// Get system analytics
    /// </summary>
    [HttpGet("analytics")]
    public async Task<ActionResult<SystemAnalyticsDto>> GetSystemAnalytics()
    {
        try
        {
            // This would be implemented to gather system-wide statistics
            var analytics = new SystemAnalyticsDto
            {
                TotalCustomers = await _customerService.GetCustomersAsync().ContinueWith(t => t.Result.Count()),
                ActiveLicenses = 0, // Implement this
                TotalUsers = 0, // Implement this
                ApiCallsToday = 0, // Implement this
                TierDistribution = new Dictionary<string, int>(), // Implement this
                RevenueMetrics = new RevenueMetricsDto() // Implement this
            };

            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system analytics");
            return StatusCode(500, new { error = "Failed to get system analytics" });
        }
    }

    /// <summary>
    /// Get audit logs with pagination
    /// </summary>
    [HttpGet("audit-logs")]
    public async Task<ActionResult<AuditLogListResponse>> GetAuditLogs(
        [FromQuery] Guid? customerId = null,
        [FromQuery] string? action = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            IEnumerable<Core.Entities.AuditLog> auditLogs;

            if (customerId.HasValue)
            {
                auditLogs = await _auditLogService.GetAuditLogsAsync(customerId.Value, page, pageSize);
            }
            else if (!string.IsNullOrEmpty(action))
            {
                auditLogs = await _auditLogService.GetAuditLogsByActionAsync(action, page, pageSize);
            }
            else
            {
                // Get all audit logs (implement this method)
                auditLogs = new List<Core.Entities.AuditLog>();
            }

            return Ok(new AuditLogListResponse
            {
                AuditLogs = auditLogs,
                Page = page,
                PageSize = pageSize,
                TotalCount = auditLogs.Count()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit logs");
            return StatusCode(500, new { error = "Failed to get audit logs" });
        }
    }
}

public class CustomerListResponse
{
    public IEnumerable<Core.Entities.Customer> Customers { get; set; } = new List<Core.Entities.Customer>();
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
}

public class UpgradeRequest
{
    public string NewTier { get; set; } = string.Empty;
}

public class SystemAnalyticsDto
{
    public int TotalCustomers { get; set; }
    public int ActiveLicenses { get; set; }
    public int TotalUsers { get; set; }
    public int ApiCallsToday { get; set; }
    public Dictionary<string, int> TierDistribution { get; set; } = new();
    public RevenueMetricsDto RevenueMetrics { get; set; } = new();
}

public class RevenueMetricsDto
{
    public decimal MonthlyRecurringRevenue { get; set; }
    public decimal AnnualRecurringRevenue { get; set; }
    public decimal AverageRevenuePerUser { get; set; }
    public double ChurnRate { get; set; }
}

public class AuditLogListResponse
{
    public IEnumerable<Core.Entities.AuditLog> AuditLogs { get; set; } = new List<Core.Entities.AuditLog>();
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
}
