using Microsoft.AspNetCore.Mvc;
using PSBEnterprise.Core.DTOs;
using PSBEnterprise.Core.Interfaces;

namespace PSBEnterprise.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class LicenseController : ControllerBase
{
    private readonly ILicenseService _licenseService;
    private readonly ILogger<LicenseController> _logger;

    public LicenseController(ILicenseService licenseService, ILogger<LicenseController> logger)
    {
        _licenseService = licenseService;
        _logger = logger;
    }

    /// <summary>
    /// Get license information for the authenticated customer
    /// </summary>
    [HttpGet("info")]
    public async Task<ActionResult<LicenseInfoDto>> GetLicenseInfo()
    {
        try
        {
            var customerId = GetCustomerIdFromContext();
            if (customerId == Guid.Empty)
            {
                return Unauthorized(new { error = "Invalid customer context" });
            }

            var licenseInfo = await _licenseService.GetLicenseInfoAsync(customerId);
            return Ok(licenseInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting license info");
            return StatusCode(500, new { error = "Failed to get license information" });
        }
    }

    /// <summary>
    /// Get usage statistics for the authenticated customer
    /// </summary>
    [HttpGet("usage")]
    public async Task<ActionResult<UsageStatsDto>> GetUsageStats([FromQuery] int days = 30)
    {
        try
        {
            var customerId = GetCustomerIdFromContext();
            if (customerId == Guid.Empty)
            {
                return Unauthorized(new { error = "Invalid customer context" });
            }

            var usageStats = await _licenseService.GetUsageStatsAsync(customerId, days);
            return Ok(usageStats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage stats for customer {CustomerId}", GetCustomerIdFromContext());
            return StatusCode(500, new { error = "Failed to get usage statistics" });
        }
    }

    /// <summary>
    /// Validate a license key and token
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<LicenseValidationResult>> ValidateLicense([FromBody] LicenseValidationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.LicenseKey) || string.IsNullOrEmpty(request.LicenseToken))
            {
                return BadRequest(new { error = "License key and token are required" });
            }

            var result = await _licenseService.ValidateLicenseAsync(request.LicenseKey, request.LicenseToken);
            
            if (!result.IsValid)
            {
                return Unauthorized(new { error = result.ErrorMessage });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating license");
            return StatusCode(500, new { error = "License validation failed" });
        }
    }

    /// <summary>
    /// Check if a specific feature is enabled for the customer
    /// </summary>
    [HttpGet("features/{feature}")]
    public async Task<ActionResult<FeatureCheckResult>> CheckFeature(string feature)
    {
        try
        {
            var customerId = GetCustomerIdFromContext();
            if (customerId == Guid.Empty)
            {
                return Unauthorized(new { error = "Invalid customer context" });
            }

            var isEnabled = await _licenseService.IsFeatureEnabledAsync(customerId, feature);
            
            return Ok(new FeatureCheckResult
            {
                Feature = feature,
                Enabled = isEnabled,
                CustomerId = customerId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking feature {Feature} for customer {CustomerId}", feature, GetCustomerIdFromContext());
            return StatusCode(500, new { error = "Failed to check feature availability" });
        }
    }

    private Guid GetCustomerIdFromContext()
    {
        // This would be set by the license validation middleware
        var customerIdClaim = HttpContext.Items["CustomerId"]?.ToString();
        return Guid.TryParse(customerIdClaim, out var customerId) ? customerId : Guid.Empty;
    }
}

public class LicenseValidationRequest
{
    public string LicenseKey { get; set; } = string.Empty;
    public string LicenseToken { get; set; } = string.Empty;
}

public class FeatureCheckResult
{
    public string Feature { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public Guid CustomerId { get; set; }
}
