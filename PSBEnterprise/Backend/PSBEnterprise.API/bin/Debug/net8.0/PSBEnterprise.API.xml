<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PSBEnterprise.API</name>
    </assembly>
    <members>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GenerateLicense(PSBEnterprise.Core.DTOs.LicenseGenerationRequest)">
            <summary>
            Generate a new license for a customer
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GetCustomers(System.Int32,System.Int32)">
            <summary>
            Get all customers with pagination
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.CreateCustomer(PSBEnterprise.Core.DTOs.CreateCustomerRequest)">
            <summary>
            Create a new customer
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GetCustomer(System.Guid)">
            <summary>
            Get customer by ID
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GetCustomerStats(System.Guid)">
            <summary>
            Get customer statistics
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.UpgradeCustomer(System.Guid,PSBEnterprise.API.Controllers.UpgradeRequest)">
            <summary>
            Upgrade customer license tier
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GetSystemAnalytics">
            <summary>
            Get system analytics
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.AdminController.GetAuditLogs(System.Nullable{System.Guid},System.String,System.Int32,System.Int32)">
            <summary>
            Get audit logs with pagination
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.LicenseController.GetLicenseInfo">
            <summary>
            Get license information for the authenticated customer
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.LicenseController.GetUsageStats(System.Int32)">
            <summary>
            Get usage statistics for the authenticated customer
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.LicenseController.ValidateLicense(PSBEnterprise.API.Controllers.LicenseValidationRequest)">
            <summary>
            Validate a license key and token
            </summary>
        </member>
        <member name="M:PSBEnterprise.API.Controllers.LicenseController.CheckFeature(System.String)">
            <summary>
            Check if a specific feature is enabled for the customer
            </summary>
        </member>
    </members>
</doc>
