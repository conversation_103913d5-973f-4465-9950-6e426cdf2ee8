{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Data Source=localhost:1521/XE;User Id=psb_user;Password=dev_password;Pooling=true;Connection Timeout=60;"}, "JwtSettings": {"SecretKey": "PSBEnterprise-Development-Secret-Key-For-JWT-Tokens-2024", "Issuer": "PSBEnterprise.API", "Audience": "PSBEnterprise.Client", "ExpirationMinutes": 60}, "LicenseSettings": {"SecretKey": "PSBEnterprise-Development-License-Secret-Key-2024", "DefaultExpirationDays": 365}}