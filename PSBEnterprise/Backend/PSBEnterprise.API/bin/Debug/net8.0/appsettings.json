{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=localhost:1521/XE;User Id=psb_user;Password=psb_password;Pooling=true;Connection Timeout=60;"}, "JwtSettings": {"SecretKey": "PSBEnterprise-Super-Secret-Key-For-JWT-Tokens-2024", "Issuer": "PSBEnterprise.API", "Audience": "PSBEnterprise.Client", "ExpirationMinutes": 60}, "LicenseSettings": {"SecretKey": "PSBEnterprise-License-Secret-Key-For-Validation-2024", "DefaultExpirationDays": 365}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/psb-enterprise-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "CorsSettings": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001"]}, "RateLimiting": {"GlobalLimit": 1000, "WindowSizeInMinutes": 1, "TierLimits": {"starter": 100, "professional": 1000, "enterprise": 10000, "custom": -1}}, "FeatureFlags": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableMetrics": true, "EnableAuditLogging": true}}