using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PSBEnterprise.Core.Interfaces;
using PSBEnterprise.Core.Services;
using PSBEnterprise.Infrastructure.Data;
using PSBEnterprise.Infrastructure.Repositories;
using Serilog;
using System.Reflection;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/psb-enterprise-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Configure Entity Framework with Oracle
builder.Services.AddDbContext<PSBEnterpriseDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    options.UseOracle(connectionString);
});

// Register repositories and services
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<ILicenseService, LicenseService>();
builder.Services.AddScoped<IFeatureFlagService, FeatureFlagService>();
builder.Services.AddScoped<IUsageTrackingService, UsageTrackingService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "https://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Configure Rate Limiting
builder.Services.AddRateLimiter(options =>
{
    options.GlobalLimiter = System.Threading.RateLimiting.PartitionedRateLimiter.Create<HttpContext, string>(
        httpContext => System.Threading.RateLimiting.RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: httpContext.User.Identity?.Name ?? httpContext.Request.Headers.Host.ToString(),
            factory: partition => new System.Threading.RateLimiting.FixedWindowRateLimiterOptions
            {
                AutoReplenishment = true,
                PermitLimit = 100,
                Window = TimeSpan.FromMinutes(1)
            }));
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "PSB Enterprise Licensing API",
        Version = "v1",
        Description = "Enterprise licensing system API for PSB applications",
        Contact = new OpenApiContact
        {
            Name = "PSB Enterprise Team",
            Email = "<EMAIL>"
        }
    });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Include XML comments
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<PSBEnterpriseDbContext>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "PSB Enterprise Licensing API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowReactApp");

app.UseRateLimiter();

app.UseAuthentication();
app.UseAuthorization();

// Add license validation middleware
app.UseMiddleware<LicenseValidationMiddleware>();

app.MapControllers();

// Add health check endpoint
app.MapHealthChecks("/health");

// Add demo endpoints for development
if (app.Environment.IsDevelopment())
{
    app.MapGet("/demo/generate-license", async (ILicenseService licenseService) =>
    {
        var request = new PSBEnterprise.Core.DTOs.LicenseGenerationRequest
        {
            CustomerId = Guid.Parse("12345678-1234-1234-1234-123456789012"),
            Tier = "enterprise",
            ExpiryDate = DateTime.UtcNow.AddYears(1)
        };

        var result = await licenseService.GenerateLicenseAsync(request);
        return Results.Ok(result);
    });
}

try
{
    Log.Information("Starting PSB Enterprise Licensing API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

// License Validation Middleware
public class LicenseValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<LicenseValidationMiddleware> _logger;

    public LicenseValidationMiddleware(RequestDelegate next, ILogger<LicenseValidationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ILicenseService licenseService)
    {
        // Skip license validation for certain paths
        var path = context.Request.Path.Value?.ToLower();
        if (path != null && (path.Contains("/health") || path.Contains("/swagger") || path.Contains("/demo")))
        {
            await _next(context);
            return;
        }

        // Check for license headers on API routes
        if (path != null && path.StartsWith("/api/") && !path.Contains("/admin/"))
        {
            var licenseKey = context.Request.Headers["X-License-Key"].FirstOrDefault();
            var licenseToken = context.Request.Headers["X-License-Token"].FirstOrDefault();

            if (string.IsNullOrEmpty(licenseKey) || string.IsNullOrEmpty(licenseToken))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("{\"error\":\"Missing license credentials\",\"code\":\"MISSING_LICENSE\"}");
                return;
            }

            try
            {
                var validation = await licenseService.ValidateLicenseAsync(licenseKey, licenseToken);
                if (!validation.IsValid)
                {
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsync($"{{\"error\":\"{validation.ErrorMessage}\",\"code\":\"INVALID_LICENSE\"}}");
                    return;
                }

                // Add customer info to context
                context.Items["CustomerId"] = validation.CustomerId.ToString();
                context.Items["Tier"] = validation.Tier;
                context.Items["License"] = validation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "License validation error");
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("{\"error\":\"License validation failed\",\"code\":\"VALIDATION_ERROR\"}");
                return;
            }
        }

        await _next(context);
    }
}
