<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSB Enterprise Licensing System - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2em;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            background: #fafafa;
        }
        
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.5;
        }
        
        .tier-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .tier-starter { background: #e3f2fd; color: #1976d2; }
        .tier-professional { background: #fff3e0; color: #f57c00; }
        .tier-enterprise { background: #f3e5f5; color: #7b1fa2; }
        
        .demo-license {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .license-key {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
            border: 1px solid #ddd;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background: #4caf50; }
        .status-trial { background: #ff9800; }
        .status-expired { background: #f44336; }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: transform 0.2s;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .architecture-diagram {
            text-align: center;
            margin: 30px 0;
        }
        
        .component-box {
            display: inline-block;
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            margin: 10px;
            min-width: 120px;
        }
        
        .arrow {
            font-size: 24px;
            color: #667eea;
            margin: 0 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">PSB</div>
            <h1>PSB Enterprise Licensing System</h1>
            <p class="subtitle">Comprehensive License Management & Feature Control Platform</p>
        </div>

        <div class="demo-section">
            <h3>🚀 System Overview</h3>
            <p>A complete enterprise-grade licensing solution built with React, C#/.NET, and Oracle Database. This system provides secure license validation, feature flagging, usage tracking, and comprehensive analytics.</p>
            
            <div class="architecture-diagram">
                <div class="component-box">React Frontend</div>
                <span class="arrow">→</span>
                <div class="component-box">.NET API</div>
                <span class="arrow">→</span>
                <div class="component-box">Oracle DB</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>💼 License Tiers</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Starter <span class="tier-badge tier-starter">$29/month</span></h4>
                    <p>Perfect for small teams</p>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li>5 Users</li>
                        <li>100 API calls/day</li>
                        <li>Basic reporting</li>
                        <li>Email support</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>Professional <span class="tier-badge tier-professional">$99/month</span></h4>
                    <p>For growing businesses</p>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li>50 Users</li>
                        <li>1,000 API calls/day</li>
                        <li>Advanced analytics</li>
                        <li>Custom branding</li>
                        <li>Priority support</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>Enterprise <span class="tier-badge tier-enterprise">$299/month</span></h4>
                    <p>For large organizations</p>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li>Unlimited users</li>
                        <li>Unlimited API calls</li>
                        <li>SSO integration</li>
                        <li>Audit logging</li>
                        <li>Dedicated support</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🔑 Demo License Information</h3>
            <div class="demo-license">
                <h4><span class="status-indicator status-active"></span>Enterprise Demo License</h4>
                <p><strong>Company:</strong> Demo Corporation</p>
                <p><strong>Status:</strong> Active</p>
                <p><strong>Expires:</strong> December 31, 2025</p>
                <p><strong>Features:</strong> All Enterprise features enabled</p>
                
                <div style="margin-top: 15px;">
                    <strong>License Key:</strong>
                    <div class="license-key">PSB-ENT-20251231-A1B2C3D4</div>
                </div>
                
                <div style="margin-top: 15px;">
                    <strong>License Token (JWT):</strong>
                    <div class="license-key">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0b21lcklkIjoiMTIzNDU2NzgtMTIzNC0xMjM0LTEyMzQtMTIzNDU2Nzg5MDEyIiwidGllciI6ImVudGVycHJpc2UiLCJleHBpcnlEYXRlIjoiMjAyNS0xMi0zMVQyMzo1OTo1OS45OTlaIiwibWF4VXNlcnMiOi0xLCJhcGlDYWxsc1BlckRheSI6LTEsImdlbmVyYXRlZEF0IjoiMjAyNC0wNy0xMVQwNjoyNzoxOC41MTdaIn0.demo-signature</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>⚡ Key Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 Secure License Validation</h4>
                    <p>JWT-based tokens with HMAC SHA-256 security and tamper detection</p>
                </div>
                
                <div class="feature-card">
                    <h4>🎛️ Feature Flagging</h4>
                    <p>Dynamic feature enabling/disabling based on license tiers</p>
                </div>
                
                <div class="feature-card">
                    <h4>📊 Usage Tracking</h4>
                    <p>Real-time monitoring of API calls, user counts, and feature usage</p>
                </div>
                
                <div class="feature-card">
                    <h4>🚨 Automatic Enforcement</h4>
                    <p>Built-in middleware for automatic limit checking and enforcement</p>
                </div>
                
                <div class="feature-card">
                    <h4>📈 Analytics Dashboard</h4>
                    <p>Comprehensive reporting and business intelligence</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔍 Audit Logging</h4>
                    <p>Complete activity tracking for compliance and security</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🛠️ Technology Stack</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div><strong>Frontend:</strong> React 18, TypeScript, Material-UI</div>
                <div><strong>Backend:</strong> .NET 8, ASP.NET Core Web API</div>
                <div><strong>Database:</strong> Oracle Database with EF Core</div>
                <div><strong>Security:</strong> JWT, HMAC SHA-256</div>
                <div><strong>Deployment:</strong> Docker, Docker Compose</div>
                <div><strong>Monitoring:</strong> Health checks, Structured logging</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 Next Steps</h3>
            <p>To run the full application:</p>
            <ol style="margin: 15px 0; padding-left: 20px;">
                <li>Ensure Docker and .NET 8 SDK are installed</li>
                <li>Run the setup script: <code>./setup.sh</code></li>
                <li>Access the frontend at <code>http://localhost:3000</code></li>
                <li>API documentation at <code>https://localhost:7001/swagger</code></li>
                <li>Use the demo license credentials above for testing</li>
            </ol>
            
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="window.open('https://localhost:7001/swagger', '_blank')">
                    🔗 API Documentation
                </button>
                <button class="btn" onclick="window.open('http://localhost:3000', '_blank')">
                    🚀 Launch Application
                </button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #666;">
            <p>PSB Enterprise Licensing System v1.0</p>
            <p>Built with ❤️ for enterprise license management</p>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards on scroll
            const cards = document.querySelectorAll('.feature-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.opacity = '1';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.transform = 'translateY(20px)';
                card.style.opacity = '0';
                card.style.transition = 'transform 0.6s ease, opacity 0.6s ease';
                observer.observe(card);
            });
            
            // Add click handlers for demo buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-2px)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
