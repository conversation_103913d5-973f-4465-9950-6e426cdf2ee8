#!/bin/bash

# PSB Enterprise Licensing System Setup Script
# Version: 1.0
# Created: 2024-07-11

set -e

echo "🚀 PSB Enterprise Licensing System Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_status "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_status "Docker Compose is installed"
    
    # Check .NET SDK
    if ! command -v dotnet &> /dev/null; then
        print_warning ".NET SDK is not installed. You'll need it for local development."
    else
        print_status ".NET SDK is installed"
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_warning "Node.js is not installed. You'll need it for local development."
    else
        print_status "Node.js is installed"
    fi
}

# Setup environment files
setup_environment() {
    print_header "Setting up Environment Files"
    
    # Backend appsettings
    if [ ! -f "Backend/PSBEnterprise.API/appsettings.Development.json" ]; then
        print_status "Creating development appsettings..."
        cat > Backend/PSBEnterprise.API/appsettings.Development.json << EOF
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=localhost:1521/XE;User Id=psb_user;Password=dev_password;Pooling=true;Connection Timeout=60;"
  },
  "JwtSettings": {
    "SecretKey": "PSBEnterprise-Development-Secret-Key-For-JWT-Tokens-2024",
    "Issuer": "PSBEnterprise.API",
    "Audience": "PSBEnterprise.Client",
    "ExpirationMinutes": 60
  },
  "LicenseSettings": {
    "SecretKey": "PSBEnterprise-Development-License-Secret-Key-2024",
    "DefaultExpirationDays": 365
  }
}
EOF
    fi
    
    # Frontend environment
    if [ ! -f "Frontend/.env.development" ]; then
        print_status "Creating frontend development environment..."
        cat > Frontend/.env.development << EOF
REACT_APP_API_BASE_URL=https://localhost:7001/api
REACT_APP_ENVIRONMENT=development
REACT_APP_VERSION=1.0.0
EOF
    fi
    
    # Frontend production environment
    if [ ! -f "Frontend/.env.production" ]; then
        print_status "Creating frontend production environment..."
        cat > Frontend/.env.production << EOF
REACT_APP_API_BASE_URL=/api
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0
EOF
    fi
}

# Setup database scripts
setup_database() {
    print_header "Setting up Database Scripts"
    
    # Create user script
    cat > Database/Scripts/create_user.sql << EOF
-- Create PSB Enterprise user
CREATE USER psb_user IDENTIFIED BY dev_password;
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE TO psb_user;
GRANT UNLIMITED TABLESPACE TO psb_user;

-- Grant additional privileges
GRANT CREATE SESSION TO psb_user;
GRANT CREATE TABLE TO psb_user;
GRANT CREATE PROCEDURE TO psb_user;
GRANT CREATE TRIGGER TO psb_user;

EXIT;
EOF
    
    # Create health check script
    cat > Database/Scripts/healthcheck.sql << EOF
SELECT 'Database is healthy' as status FROM dual;
EXIT;
EOF
    
    print_status "Database scripts created"
}

# Setup Docker files
setup_docker() {
    print_header "Setting up Docker Files"
    
    # Backend Dockerfile
    mkdir -p Backend/PSBEnterprise.API
    cat > Backend/PSBEnterprise.API/Dockerfile << EOF
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["PSBEnterprise.API/PSBEnterprise.API.csproj", "PSBEnterprise.API/"]
COPY ["PSBEnterprise.Core/PSBEnterprise.Core.csproj", "PSBEnterprise.Core/"]
COPY ["PSBEnterprise.Infrastructure/PSBEnterprise.Infrastructure.csproj", "PSBEnterprise.Infrastructure/"]
RUN dotnet restore "PSBEnterprise.API/PSBEnterprise.API.csproj"
COPY . .
WORKDIR "/src/PSBEnterprise.API"
RUN dotnet build "PSBEnterprise.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "PSBEnterprise.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PSBEnterprise.API.dll"]
EOF
    
    # Frontend Dockerfile
    cat > Frontend/Dockerfile << EOF
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine AS production
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF
    
    # Frontend nginx config
    cat > Frontend/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        location / {
            try_files \$uri \$uri/ /index.html;
        }
        
        location /api {
            proxy_pass http://psb-api:80;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF
    
    print_status "Docker files created"
}

# Install dependencies
install_dependencies() {
    print_header "Installing Dependencies"
    
    # Backend dependencies
    if [ -d "Backend" ]; then
        print_status "Restoring .NET packages..."
        cd Backend
        dotnet restore
        cd ..
    fi
    
    # Frontend dependencies
    if [ -d "Frontend" ]; then
        print_status "Installing Node.js packages..."
        cd Frontend
        npm install
        cd ..
    fi
}

# Build the application
build_application() {
    print_header "Building Application"
    
    # Build backend
    if [ -d "Backend" ]; then
        print_status "Building .NET application..."
        cd Backend
        dotnet build --configuration Release
        cd ..
    fi
    
    # Build frontend
    if [ -d "Frontend" ]; then
        print_status "Building React application..."
        cd Frontend
        npm run build
        cd ..
    fi
}

# Start services
start_services() {
    print_header "Starting Services"
    
    print_status "Starting Docker services..."
    docker-compose up -d
    
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    print_status "Checking service health..."
    
    # Check Oracle
    if docker-compose ps oracle-db | grep -q "Up"; then
        print_status "Oracle Database is running"
    else
        print_warning "Oracle Database may not be ready yet"
    fi
    
    # Check API
    if docker-compose ps psb-api | grep -q "Up"; then
        print_status "API service is running"
    else
        print_warning "API service may not be ready yet"
    fi
    
    # Check Frontend
    if docker-compose ps psb-frontend | grep -q "Up"; then
        print_status "Frontend service is running"
    else
        print_warning "Frontend service may not be ready yet"
    fi
}

# Display final information
show_completion_info() {
    print_header "Setup Complete!"
    
    echo ""
    echo "🎉 PSB Enterprise Licensing System is now running!"
    echo ""
    echo "📱 Application URLs:"
    echo "   Frontend:  http://localhost:3000"
    echo "   API:       https://localhost:7001"
    echo "   Swagger:   https://localhost:7001/swagger"
    echo "   Health:    https://localhost:7001/health"
    echo ""
    echo "🗄️  Database:"
    echo "   Oracle XE: localhost:1521/XE"
    echo "   Username:  psb_user"
    echo "   Password:  dev_password"
    echo ""
    echo "🔧 Management:"
    echo "   Logs:      docker-compose logs -f"
    echo "   Stop:      docker-compose down"
    echo "   Restart:   docker-compose restart"
    echo ""
    echo "📚 Demo License:"
    echo "   Generate:  curl http://localhost:7000/demo/generate-license"
    echo ""
    echo "🚀 Next Steps:"
    echo "   1. Visit http://localhost:3000 to access the application"
    echo "   2. Generate a demo license for testing"
    echo "   3. Explore the API documentation at https://localhost:7001/swagger"
    echo "   4. Check the user manual in docs/user-manual.md"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    setup_environment
    setup_database
    setup_docker
    
    # Ask user what they want to do
    echo ""
    echo "What would you like to do?"
    echo "1) Full setup (install dependencies, build, and start services)"
    echo "2) Install dependencies only"
    echo "3) Build application only"
    echo "4) Start services only"
    echo "5) Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            install_dependencies
            build_application
            start_services
            show_completion_info
            ;;
        2)
            install_dependencies
            ;;
        3)
            build_application
            ;;
        4)
            start_services
            show_completion_info
            ;;
        5)
            print_status "Setup cancelled by user"
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
