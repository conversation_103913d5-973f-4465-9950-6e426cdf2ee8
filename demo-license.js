const LicenseManager = require('./services/license-service/license-manager');

// Create a demo license for testing
const licenseManager = new LicenseManager('your-super-secret-key-for-development-only');

const customerId = 'customer-123';
const tier = 'enterprise';
const expiryDate = new Date();
expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year from now

const license = licenseManager.generateLicense(customerId, tier, expiryDate);

console.log('\n=== DEMO LICENSE GENERATED ===');
console.log('Customer ID:', customerId);
console.log('Tier:', tier);
console.log('License Key:', license.licenseKey);
console.log('License Token:', license.token);
console.log('Expires:', expiryDate.toISOString());
console.log('\n=== USE THESE IN YOUR API CALLS ===');
console.log('Headers:');
console.log('X-License-Key:', license.licenseKey);
console.log('X-License-Token:', license.token);
console.log('\n=== CURL EXAMPLE ===');
console.log(`curl -H "X-License-Key: ${license.licenseKey}" -H "X-License-Token: ${license.token}" http://localhost:3000/api/license/info`);
console.log('===============================\n');

// Test validation
const validation = licenseManager.validateLicense(license.licenseKey, license.token);
console.log('Validation test:', validation.valid ? 'PASSED' : 'FAILED');
if (!validation.valid) {
  console.log('Validation error:', validation.reason);
}
